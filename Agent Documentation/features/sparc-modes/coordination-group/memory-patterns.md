# Memory Patterns

Knowledge management patterns for coordination-focused SPARC modes.

## Overview

Memory patterns define how Innovator, Memory Manager, and Swarm Coordinator modes store, organize, retrieve, and utilize knowledge for effective coordination.

## Core Memory Architectures

### 1. Hierarchical Memory Pattern

**Tree-Based Organization**
```javascript
class HierarchicalMemory {
  constructor() {
    this.root = {
      id: 'root',
      children: new Map(),
      metadata: new Map()
    };
    this.index = new Map();
    this.paths = new Map();
  }
  
  store(path, data, metadata = {}) {
    const segments = path.split('/');
    let current = this.root;
    
    for (let i = 0; i < segments.length - 1; i++) {
      const segment = segments[i];
      
      if (!current.children.has(segment)) {
        current.children.set(segment, {
          id: `${current.id}/${segment}`,
          children: new Map(),
          metadata: new Map()
        });
      }
      
      current = current.children.get(segment);
    }
    
    const leafName = segments[segments.length - 1];
    const leaf = {
      id: `${current.id}/${leafName}`,
      data,
      metadata: {
        ...metadata,
        created: Date.now(),
        path,
        depth: segments.length
      }
    };
    
    current.children.set(leafName, leaf);
    this.index.set(leaf.id, leaf);
    this.paths.set(path, leaf.id);
    
    return leaf.id;
  }
  
  retrieve(pathOrId) {
    if (pathOrId.startsWith('/')) {
      return this.index.get(this.paths.get(pathOrId));
    }
    return this.index.get(pathOrId);
  }
  
  query(pattern, options = {}) {
    const results = [];
    const regex = new RegExp(pattern);
    
    const traverse = (node, depth = 0) => {
      if (options.maxDepth && depth > options.maxDepth) return;
      
      if (regex.test(node.id)) {
        results.push(node);
      }
      
      if (node.children) {
        for (const child of node.children.values()) {
          traverse(child, depth + 1);
        }
      }
    };
    
    traverse(this.root);
    return results;
  }
}
```

**Layered Memory**
```javascript
class LayeredMemory {
  constructor() {
    this.layers = new Map([
      ['immediate', new ShortTermMemory()],
      ['working', new WorkingMemory()],
      ['episodic', new EpisodicMemory()],
      ['semantic', new SemanticMemory()],
      ['procedural', new ProceduralMemory()]
    ]);
    this.transitions = new Map();
  }
  
  store(data, layer = 'working') {
    const memory = this.layers.get(layer);
    const id = memory.store(data);
    
    // Check for promotion/demotion
    this.evaluateTransitions(id, layer);
    
    return { id, layer };
  }
  
  evaluateTransitions(id, currentLayer) {
    const memory = this.layers.get(currentLayer);
    const data = memory.retrieve(id);
    
    const transitions = this.transitions.get(currentLayer) || [];
    
    for (const transition of transitions) {
      if (transition.condition(data)) {
        this.transition(id, currentLayer, transition.target);
      }
    }
  }
  
  defineTransition(from, to, condition) {
    if (!this.transitions.has(from)) {
      this.transitions.set(from, []);
    }
    
    this.transitions.get(from).push({
      target: to,
      condition
    });
  }
}
```

### 2. Associative Memory Pattern

**Content-Addressable Memory**
```javascript
class AssociativeMemory {
  constructor() {
    this.memories = new Map();
    this.associations = new Graph();
    this.features = new FeatureExtractor();
  }
  
  store(content, context = {}) {
    const features = this.features.extract(content);
    const id = this.generateId(features);
    
    const memory = {
      id,
      content,
      features,
      context,
      timestamp: Date.now(),
      strength: 1.0
    };
    
    this.memories.set(id, memory);
    this.createAssociations(memory);
    
    return id;
  }
  
  recall(cue, threshold = 0.5) {
    const cueFeatures = this.features.extract(cue);
    const candidates = [];
    
    for (const [id, memory] of this.memories) {
      const similarity = this.calculateSimilarity(
        cueFeatures,
        memory.features
      );
      
      if (similarity >= threshold) {
        candidates.push({
          memory,
          similarity,
          associations: this.getAssociations(id)
        });
      }
    }
    
    return candidates.sort((a, b) => b.similarity - a.similarity);
  }
  
  createAssociations(memory) {
    const similar = this.findSimilar(memory, 5);
    
    for (const sim of similar) {
      const weight = sim.similarity * memory.strength;
      this.associations.addEdge(memory.id, sim.id, weight);
    }
  }
}
```

**Semantic Network Memory**
```javascript
class SemanticNetworkMemory {
  constructor() {
    this.nodes = new Map();
    this.edges = new Map();
    this.concepts = new Map();
  }
  
  addConcept(name, properties = {}) {
    const id = this.generateConceptId(name);
    
    const concept = {
      id,
      name,
      properties,
      instances: new Set(),
      relations: new Map()
    };
    
    this.concepts.set(id, concept);
    this.nodes.set(id, { type: 'concept', data: concept });
    
    return id;
  }
  
  addRelation(source, target, type, properties = {}) {
    const id = `${source}-${type}-${target}`;
    
    const relation = {
      id,
      source,
      target,
      type,
      properties,
      strength: properties.strength || 1.0
    };
    
    this.edges.set(id, relation);
    
    // Update concept relations
    if (this.concepts.has(source)) {
      this.concepts.get(source).relations.set(target, relation);
    }
    
    return id;
  }
  
  query(pattern) {
    const compiled = this.compileQuery(pattern);
    const results = [];
    
    const traverse = (node, path, bindings) => {
      if (this.matchesPattern(node, pattern.node, bindings)) {
        if (pattern.relations) {
          for (const rel of pattern.relations) {
            const edges = this.getEdges(node.id, rel.type);
            
            for (const edge of edges) {
              const nextNode = this.nodes.get(edge.target);
              traverse(nextNode, [...path, edge], bindings);
            }
          }
        } else {
          results.push({ path, bindings });
        }
      }
    };
    
    for (const node of this.nodes.values()) {
      traverse(node, [], new Map());
    }
    
    return results;
  }
}
```

### 3. Distributed Memory Pattern

**Sharded Memory**
```javascript
class ShardedMemory {
  constructor(shardCount = 16) {
    this.shards = new Array(shardCount)
      .fill(null)
      .map(() => new MemoryShard());
    this.router = new ConsistentHashRouter(shardCount);
    this.replication = 3;
  }
  
  store(key, value, metadata = {}) {
    const primary = this.router.route(key);
    const replicas = this.router.getReplicas(key, this.replication);
    
    // Store on primary
    const result = this.shards[primary].store(key, value, metadata);
    
    // Replicate asynchronously
    this.replicate(key, value, metadata, replicas);
    
    return {
      key,
      shard: primary,
      replicas,
      version: result.version
    };
  }
  
  retrieve(key, consistency = 'eventual') {
    if (consistency === 'strong') {
      return this.strongRead(key);
    }
    
    const shard = this.router.route(key);
    return this.shards[shard].retrieve(key);
  }
  
  strongRead(key) {
    const replicas = [
      this.router.route(key),
      ...this.router.getReplicas(key, this.replication)
    ];
    
    const results = replicas.map(shard => 
      this.shards[shard].retrieve(key)
    );
    
    return this.reconcile(results);
  }
}
```

**Federated Memory**
```javascript
class FederatedMemory {
  constructor() {
    this.federates = new Map();
    this.catalog = new MemoryCatalog();
    this.router = new FederationRouter();
  }
  
  registerFederate(id, endpoint, capabilities) {
    this.federates.set(id, {
      endpoint,
      capabilities,
      health: 'healthy',
      lastSeen: Date.now()
    });
    
    this.catalog.update(id, capabilities);
  }
  
  async query(request) {
    const federates = this.router.selectFederates(request);
    
    const queries = federates.map(fed => ({
      federate: fed,
      promise: this.queryFederate(fed, request)
    }));
    
    const results = await Promise.allSettled(
      queries.map(q => q.promise)
    );
    
    return this.mergeResults(results, request);
  }
  
  async store(data, policy = {}) {
    const federates = this.router.selectForStorage(data, policy);
    
    const stores = federates.map(fed => 
      this.storeFederate(fed, data)
    );
    
    const results = await Promise.allSettled(stores);
    
    return {
      stored: results.filter(r => r.status === 'fulfilled').length,
      total: federates.length,
      locations: results
        .filter(r => r.status === 'fulfilled')
        .map(r => r.value)
    };
  }
}
```

## Mode-Specific Memory Patterns

### Innovator Mode Patterns

**Inspiration Memory**
```javascript
class InspirationMemory {
  constructor() {
    this.inspirations = new Map();
    this.connections = new Graph();
    this.patterns = new PatternMatcher();
  }
  
  captureInspiration(source, content, context) {
    const features = this.extractFeatures(content);
    const patterns = this.patterns.match(content);
    
    const inspiration = {
      id: this.generateId(),
      source,
      content,
      features,
      patterns,
      context,
      timestamp: Date.now(),
      applications: []
    };
    
    this.inspirations.set(inspiration.id, inspiration);
    this.linkRelated(inspiration);
    
    return inspiration;
  }
  
  findRelevant(problem) {
    const problemFeatures = this.extractFeatures(problem);
    const candidates = [];
    
    for (const inspiration of this.inspirations.values()) {
      const relevance = this.calculateRelevance(
        problemFeatures,
        inspiration.features
      );
      
      if (relevance > 0.3) {
        candidates.push({
          inspiration,
          relevance,
          applications: this.suggestApplications(problem, inspiration)
        });
      }
    }
    
    return candidates.sort((a, b) => b.relevance - a.relevance);
  }
}
```

**Creative Process Memory**
```javascript
class CreativeProcessMemory {
  constructor() {
    this.processes = new Map();
    this.outcomes = new Map();
    this.effectiveness = new Map();
  }
  
  recordProcess(session) {
    const process = {
      id: session.id,
      steps: session.steps,
      techniques: session.techniques,
      participants: session.participants,
      duration: session.duration,
      context: session.context
    };
    
    this.processes.set(process.id, process);
    
    // Link to outcomes
    if (session.outcomes) {
      this.outcomes.set(process.id, session.outcomes);
      this.updateEffectiveness(process, session.outcomes);
    }
    
    return process.id;
  }
  
  recommendProcess(context) {
    const similar = this.findSimilarContexts(context);
    const recommendations = [];
    
    for (const sim of similar) {
      const process = this.processes.get(sim.id);
      const effectiveness = this.effectiveness.get(sim.id) || 0;
      
      recommendations.push({
        process,
        similarity: sim.similarity,
        effectiveness,
        score: sim.similarity * effectiveness
      });
    }
    
    return recommendations.sort((a, b) => b.score - a.score);
  }
}
```

### Memory Manager Mode Patterns

**Versioned Memory**
```javascript
class VersionedMemory {
  constructor() {
    this.objects = new Map();
    this.versions = new Map();
    this.branches = new Map();
  }
  
  store(key, value, metadata = {}) {
    if (!this.objects.has(key)) {
      this.objects.set(key, {
        id: key,
        versions: [],
        branches: new Map([['main', { head: null }]])
      });
    }
    
    const obj = this.objects.get(key);
    const branch = metadata.branch || 'main';
    
    const version = {
      id: this.generateVersionId(),
      value,
      parent: obj.branches.get(branch).head,
      timestamp: Date.now(),
      author: metadata.author,
      message: metadata.message
    };
    
    obj.versions.push(version);
    obj.branches.get(branch).head = version.id;
    
    this.versions.set(version.id, version);
    
    return version.id;
  }
  
  retrieve(key, options = {}) {
    const obj = this.objects.get(key);
    if (!obj) return null;
    
    const branch = options.branch || 'main';
    const version = options.version || obj.branches.get(branch).head;
    
    return this.versions.get(version);
  }
  
  history(key, options = {}) {
    const obj = this.objects.get(key);
    if (!obj) return [];
    
    const branch = options.branch || 'main';
    const history = [];
    
    let current = obj.branches.get(branch).head;
    
    while (current && history.length < (options.limit || Infinity)) {
      const version = this.versions.get(current);
      history.push(version);
      current = version.parent;
    }
    
    return history;
  }
}
```

**Index-Based Memory**
```javascript
class IndexedMemory {
  constructor() {
    this.storage = new Map();
    this.indices = new Map();
    this.indexers = new Map();
  }
  
  defineIndex(name, indexer) {
    this.indexers.set(name, indexer);
    this.indices.set(name, new Map());
    
    // Reindex existing data
    for (const [key, value] of this.storage) {
      this.updateIndex(name, key, value);
    }
  }
  
  store(key, value) {
    this.storage.set(key, value);
    
    // Update all indices
    for (const [name, indexer] of this.indexers) {
      this.updateIndex(name, key, value);
    }
    
    return key;
  }
  
  query(index, value) {
    const idx = this.indices.get(index);
    if (!idx) return [];
    
    const keys = idx.get(value) || new Set();
    const results = [];
    
    for (const key of keys) {
      results.push({
        key,
        value: this.storage.get(key)
      });
    }
    
    return results;
  }
  
  updateIndex(indexName, key, value) {
    const indexer = this.indexers.get(indexName);
    const indexValues = indexer(value);
    
    const index = this.indices.get(indexName);
    
    for (const indexValue of indexValues) {
      if (!index.has(indexValue)) {
        index.set(indexValue, new Set());
      }
      
      index.get(indexValue).add(key);
    }
  }
}
```

### Swarm Coordinator Mode Patterns

**Collective Memory**
```javascript
class CollectiveMemory {
  constructor() {
    this.localMemories = new Map();
    this.sharedMemory = new SharedMemory();
    this.consensus = new MemoryConsensus();
  }
  
  registerAgent(agentId) {
    this.localMemories.set(agentId, new LocalMemory());
  }
  
  storeLocal(agentId, key, value) {
    const local = this.localMemories.get(agentId);
    return local.store(key, value);
  }
  
  async shareMemory(agentId, key, value) {
    // Store locally first
    this.storeLocal(agentId, key, value);
    
    // Propose to collective
    const proposal = {
      key,
      value,
      proposer: agentId,
      timestamp: Date.now()
    };
    
    const consensus = await this.consensus.propose(proposal);
    
    if (consensus.accepted) {
      this.sharedMemory.store(key, value, {
        consensus: consensus.id,
        proposer: agentId
      });
      
      // Propagate to other agents
      this.propagate(key, value, agentId);
    }
    
    return consensus;
  }
  
  queryCollective(query) {
    // Query shared memory
    const shared = this.sharedMemory.query(query);
    
    // Aggregate local memories
    const local = this.aggregateLocal(query);
    
    return this.mergeResults(shared, local);
  }
}
```

**Stigmergic Memory**
```javascript
class StigmergicMemory {
  constructor() {
    this.environment = new Map();
    this.pheromones = new Map();
    this.decay = 0.1;
  }
  
  deposit(location, type, intensity) {
    const key = `${location}-${type}`;
    
    const current = this.pheromones.get(key) || 0;
    this.pheromones.set(key, current + intensity);
    
    // Schedule decay
    this.scheduleDecay(key);
  }
  
  sense(location, type) {
    const key = `${location}-${type}`;
    return this.pheromones.get(key) || 0;
  }
  
  gradient(location, type, radius = 1) {
    const neighbors = this.getNeighbors(location, radius);
    const gradient = new Map();
    
    for (const neighbor of neighbors) {
      const intensity = this.sense(neighbor, type);
      gradient.set(neighbor, intensity);
    }
    
    return gradient;
  }
  
  scheduleDecay(key) {
    setTimeout(() => {
      const current = this.pheromones.get(key);
      const decayed = current * (1 - this.decay);
      
      if (decayed > 0.01) {
        this.pheromones.set(key, decayed);
        this.scheduleDecay(key);
      } else {
        this.pheromones.delete(key);
      }
    }, 1000);
  }
}
```

## Memory Operations

### 1. Memory Consolidation

```javascript
class MemoryConsolidation {
  constructor() {
    this.consolidators = new Map();
    this.schedules = new Map();
  }
  
  defineConsolidator(type, consolidator) {
    this.consolidators.set(type, {
      process: consolidator.process,
      condition: consolidator.condition,
      schedule: consolidator.schedule
    });
  }
  
  async consolidate(memoryType) {
    const consolidator = this.consolidators.get(memoryType);
    const memories = await this.getMemoriesToConsolidate(memoryType);
    
    const consolidated = [];
    
    for (const batch of this.batch(memories, 100)) {
      const result = await consolidator.process(batch);
      consolidated.push(result);
    }
    
    return this.mergeConsolidated(consolidated);
  }
  
  scheduleConsolidation(type, schedule) {
    this.schedules.set(type, schedule);
    
    const run = async () => {
      if (await this.shouldConsolidate(type)) {
        await this.consolidate(type);
      }
      
      setTimeout(run, schedule.interval);
    };
    
    setTimeout(run, schedule.initialDelay);
  }
}
```

### 2. Memory Compression

```javascript
class MemoryCompression {
  constructor() {
    this.compressors = new Map();
    this.decompressors = new Map();
  }
  
  compress(data, method = 'auto') {
    if (method === 'auto') {
      method = this.selectCompressor(data);
    }
    
    const compressor = this.compressors.get(method);
    
    return {
      method,
      compressed: compressor.compress(data),
      metadata: {
        originalSize: this.sizeof(data),
        compressedSize: this.sizeof(compressor.compress(data)),
        ratio: this.sizeof(compressor.compress(data)) / this.sizeof(data)
      }
    };
  }
  
  selectCompressor(data) {
    let bestMethod = null;
    let bestRatio = 1.0;
    
    for (const [method, compressor] of this.compressors) {
      const compressed = compressor.compress(data);
      const ratio = this.sizeof(compressed) / this.sizeof(data);
      
      if (ratio < bestRatio) {
        bestMethod = method;
        bestRatio = ratio;
      }
    }
    
    return bestMethod || 'none';
  }
}
```

### 3. Memory Garbage Collection

```javascript
class MemoryGarbageCollector {
  constructor() {
    this.policies = new Map();
    this.marks = new Map();
    this.statistics = new Map();
  }
  
  definePolicy(name, policy) {
    this.policies.set(name, {
      shouldCollect: policy.shouldCollect,
      mark: policy.mark,
      sweep: policy.sweep,
      compact: policy.compact
    });
  }
  
  async collect(memoryPool, policyName) {
    const policy = this.policies.get(policyName);
    
    // Mark phase
    const marked = await this.markPhase(memoryPool, policy);
    
    // Sweep phase
    const swept = await this.sweepPhase(memoryPool, marked, policy);
    
    // Compact phase
    if (policy.compact) {
      await this.compactPhase(memoryPool, policy);
    }
    
    return {
      marked: marked.size,
      swept: swept.length,
      reclaimed: swept.reduce((sum, m) => sum + m.size, 0)
    };
  }
}
```

## Memory Access Patterns

### 1. Cache Patterns

```javascript
class MemoryCache {
  constructor(config = {}) {
    this.cache = new Map();
    this.maxSize = config.maxSize || 1000;
    this.ttl = config.ttl || 3600000;
    this.strategy = config.strategy || 'lru';
    this.access = new Map();
  }
  
  get(key) {
    if (this.cache.has(key)) {
      const entry = this.cache.get(key);
      
      if (this.isValid(entry)) {
        this.recordAccess(key);
        return entry.value;
      } else {
        this.cache.delete(key);
      }
    }
    
    return null;
  }
  
  set(key, value, ttl = this.ttl) {
    if (this.cache.size >= this.maxSize) {
      this.evict();
    }
    
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl,
      created: Date.now()
    });
    
    this.recordAccess(key);
  }
  
  evict() {
    switch (this.strategy) {
      case 'lru':
        return this.evictLRU();
      case 'lfu':
        return this.evictLFU();
      case 'fifo':
        return this.evictFIFO();
      default:
        return this.evictRandom();
    }
  }
}
```

### 2. Prefetch Patterns

```javascript
class MemoryPrefetcher {
  constructor() {
    this.patterns = new Map();
    this.predictions = new Map();
    this.prefetchQueue = [];
  }
  
  recordAccess(key) {
    const pattern = this.detectPattern(key);
    
    if (pattern) {
      const predictions = this.predict(pattern);
      this.schedulePrefetch(predictions);
    }
  }
  
  detectPattern(key) {
    // Implement pattern detection logic
    return {
      type: 'sequential',
      stride: 1,
      confidence: 0.8
    };
  }
  
  predict(pattern) {
    switch (pattern.type) {
      case 'sequential':
        return this.predictSequential(pattern);
      case 'strided':
        return this.predictStrided(pattern);
      case 'correlated':
        return this.predictCorrelated(pattern);
      default:
        return [];
    }
  }
  
  schedulePrefetch(predictions) {
    for (const prediction of predictions) {
      if (prediction.confidence > 0.7) {
        this.prefetchQueue.push({
          key: prediction.key,
          priority: prediction.confidence,
          deadline: Date.now() + prediction.window
        });
      }
    }
    
    this.processPrefetchQueue();
  }
}
```

## Performance Optimization

### 1. Memory Pooling

```javascript
class MemoryPool {
  constructor(config) {
    this.blockSize = config.blockSize;
    this.poolSize = config.poolSize;
    this.free = [];
    this.used = new Set();
    
    this.initialize();
  }
  
  initialize() {
    for (let i = 0; i < this.poolSize; i++) {
      this.free.push(this.allocateBlock());
    }
  }
  
  acquire() {
    if (this.free.length === 0) {
      this.grow();
    }
    
    const block = this.free.pop();
    this.used.add(block);
    
    return block;
  }
  
  release(block) {
    if (this.used.has(block)) {
      this.used.delete(block);
      this.reset(block);
      this.free.push(block);
    }
  }
  
  grow() {
    const newSize = Math.ceil(this.poolSize * 0.5);
    
    for (let i = 0; i < newSize; i++) {
      this.free.push(this.allocateBlock());
    }
    
    this.poolSize += newSize;
  }
}
```

### 2. Memory Alignment

```javascript
class AlignedMemory {
  constructor() {
    this.alignment = 64; // Cache line size
    this.allocations = new Map();
  }
  
  allocate(size) {
    const alignedSize = this.alignSize(size);
    const buffer = new ArrayBuffer(alignedSize);
    
    const allocation = {
      buffer,
      size: alignedSize,
      used: size,
      alignment: this.alignment
    };
    
    const id = this.generateId();
    this.allocations.set(id, allocation);
    
    return {
      id,
      view: new DataView(buffer),
      offset: 0
    };
  }
  
  alignSize(size) {
    return Math.ceil(size / this.alignment) * this.alignment;
  }
}
```

## Best Practices

1. **Pattern Selection**
   - Match memory pattern to access patterns
   - Consider consistency requirements
   - Balance between performance and complexity

2. **Memory Management**
   - Implement proper garbage collection
   - Use compression for large datasets
   - Monitor memory usage and growth

3. **Performance Optimization**
   - Use appropriate caching strategies
   - Implement prefetching for predictable access
   - Pool memory for frequent allocations

4. **Scalability Considerations**
   - Design for distributed memory from the start
   - Implement sharding for large datasets
   - Use replication for fault tolerance