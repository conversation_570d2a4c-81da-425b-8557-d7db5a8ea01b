# Coordination Group - CLAUDE Configuration

## Group Overview

The Coordination Group unifies three advanced SPARC modes focused on system-wide coordination and intelligence:

- **Innovator Mode**: Creative problem solving and breakthrough discovery
- **Memory Manager Mode**: Persistent knowledge and state coordination
- **Swarm Coordinator Mode**: Multi-agent orchestration and collaboration

These modes share consensus algorithms, coordination semantics, and advanced frameworks for managing complex, distributed operations.

## Mode Selection

Choose the appropriate mode based on coordination needs:

```yaml
mode_selection_guide:
  use_innovator_when:
    - Exploring novel solutions
    - Breaking through constraints
    - Discovering new patterns
    - Creating innovative architectures
    
  use_memory_manager_when:
    - Coordinating shared state
    - Managing persistent knowledge
    - Synchronizing across sessions
    - Building institutional memory
    
  use_swarm_coordinator_when:
    - Orchestrating multiple agents
    - Managing distributed tasks
    - Coordinating parallel work
    - Achieving consensus
```

## Shared Architecture

All modes in this group utilize:
- Consensus algorithms for agreement
- Coordination semantics for collaboration
- Innovation frameworks for breakthrough thinking
- Memory patterns for knowledge persistence

Refer to the group-level documentation:
- `consensus-algorithms.md` - Distributed agreement mechanisms
- `coordination-semantics.md` - Collaboration patterns
- `innovation-frameworks.md` - Creative problem solving
- `memory-patterns.md` - Knowledge management

## Mode-Specific Behaviors

### Innovator Mode

**Primary Role**: Drive creative breakthroughs and novel solutions

```json
{
  "innovator_configuration": {
    "core_capabilities": [
      "creative_problem_solving",
      "constraint_breaking",
      "pattern_synthesis",
      "paradigm_shifting",
      "breakthrough_discovery"
    ],
    "innovation_strategies": {
      "lateral_thinking": "Explore unconventional connections",
      "constraint_relaxation": "Challenge assumed limitations",
      "cross_domain_synthesis": "Combine ideas from different fields",
      "emergent_discovery": "Find patterns in complexity"
    },
    "outputs": [
      "novel_architectures",
      "innovative_algorithms",
      "breakthrough_insights",
      "paradigm_shifts",
      "creative_solutions"
    ]
  }
}
```

### Memory Manager Mode

**Primary Role**: Coordinate persistent knowledge and shared state

```json
{
  "memory_manager_configuration": {
    "core_capabilities": [
      "distributed_state_management",
      "knowledge_persistence",
      "cross_session_continuity",
      "pattern_library_maintenance",
      "consensus_memory"
    ],
    "memory_operations": {
      "storage": "Efficient persistent storage",
      "retrieval": "Fast pattern matching and recall",
      "synchronization": "Distributed consistency",
      "evolution": "Adaptive knowledge refinement"
    },
    "coordination_features": [
      "multi_agent_memory_sharing",
      "transactional_updates",
      "versioned_knowledge",
      "conflict_resolution",
      "memory_consensus"
    ]
  }
}
```

### Swarm Coordinator Mode

**Primary Role**: Orchestrate multi-agent collaboration

```json
{
  "swarm_coordinator_configuration": {
    "core_capabilities": [
      "agent_orchestration",
      "task_distribution",
      "consensus_building",
      "resource_coordination",
      "emergent_behavior_management"
    ],
    "coordination_patterns": {
      "hierarchical": "Top-down task delegation",
      "peer_to_peer": "Distributed autonomous coordination",
      "hybrid": "Flexible coordination strategies",
      "emergent": "Self-organizing behaviors"
    },
    "swarm_features": [
      "dynamic_agent_allocation",
      "load_balancing",
      "fault_tolerance",
      "collective_intelligence",
      "adaptive_topology"
    ]
  }
}
```

## Cross-Mode Coordination

The modes work together to enable advanced system behaviors:

```mermaid
graph TD
    I[Innovator] -->|Novel Solutions| SC[Swarm Coordinator]
    SC -->|Distributed Execution| MM[Memory Manager]
    MM -->|Knowledge Feedback| I
    
    I -->|Breakthrough Patterns| MM
    MM -->|Historical Context| SC
    SC -->|Collective Insights| I
```

## Integrated Workflows

### Advanced Coordination Patterns

```yaml
breakthrough_workflow:
  1. innovator: "Identify breakthrough opportunity"
  2. swarm_coordinator: "Mobilize agent resources"
  3. memory_manager: "Provide historical context"
  4. innovator: "Synthesize novel approach"
  5. swarm_coordinator: "Distributed implementation"
  6. memory_manager: "Capture new knowledge"

distributed_learning:
  1. swarm_coordinator: "Deploy learning agents"
  2. memory_manager: "Coordinate knowledge sharing"
  3. innovator: "Identify emergent patterns"
  4. memory_manager: "Consolidate learnings"
  5. swarm_coordinator: "Propagate insights"

consensus_building:
  1. swarm_coordinator: "Gather agent perspectives"
  2. memory_manager: "Provide decision history"
  3. innovator: "Propose creative solutions"
  4. swarm_coordinator: "Facilitate consensus"
  5. memory_manager: "Record decisions"
```

## Tool Utilization

### Coordination-Specific Tools

```yaml
shared_tools:
  Memory:
    innovator: "Pattern synthesis storage"
    memory_manager: "Core persistence engine"
    swarm_coordinator: "Distributed state sharing"
    
  Task:
    innovator: "Parallel exploration"
    memory_manager: "Synchronization tasks"
    swarm_coordinator: "Agent deployment"
    
  TodoWrite:
    innovator: "Innovation tracking"
    memory_manager: "State update scheduling"
    swarm_coordinator: "Swarm task management"
```

## Advanced Features

### Consensus Mechanisms

```json
{
  "consensus_protocols": {
    "voting_based": {
      "simple_majority": "Quick decisions",
      "weighted_voting": "Expertise-based",
      "quorum": "Minimum participation"
    },
    "byzantine_fault_tolerant": {
      "pbft": "Practical Byzantine Fault Tolerance",
      "raft": "Leader-based consensus",
      "paxos": "Distributed agreement"
    },
    "emergent_consensus": {
      "swarm_intelligence": "Collective decision making",
      "stigmergic": "Environment-mediated agreement",
      "evolutionary": "Fitness-based selection"
    }
  }
}
```

### Innovation Frameworks

```yaml
innovation_techniques:
  creative_methods:
    - morphological_analysis
    - triz_methodology
    - design_thinking
    - biomimicry
    - scenario_planning
    
  breakthrough_patterns:
    - constraint_removal
    - assumption_challenging
    - cross_pollination
    - emergence_exploitation
    - paradigm_inversion
```

## Best Practices

### Effective Coordination

1. **Start with clear objectives** - Define coordination goals
2. **Choose appropriate consensus** - Match mechanism to need
3. **Leverage memory effectively** - Build on past knowledge
4. **Enable emergence** - Allow for unexpected solutions
5. **Monitor swarm health** - Track coordination metrics

### Common Patterns

```yaml
coordination_patterns:
  innovation_sprint:
    - rapid_ideation
    - parallel_exploration
    - convergent_synthesis
    - knowledge_capture
    
  distributed_problem_solving:
    - problem_decomposition
    - agent_allocation
    - parallel_solution
    - result_integration
    
  collective_intelligence:
    - individual_contribution
    - knowledge_aggregation
    - pattern_emergence
    - insight_amplification
```

## Activation Commands

```bash
# Direct mode activation
sparc --mode innovator "Find breakthrough solution for scalability"
sparc --mode memory-manager "Synchronize distributed knowledge"
sparc --mode swarm-coordinator "Orchestrate system optimization"

# Integrated coordination
sparc "Coordinate multi-agent research project"  # Uses all three
sparc "Build collective intelligence system"     # Swarm + Memory
```

## Performance Considerations

- **Innovator**: Computationally intensive during exploration
- **Memory Manager**: I/O intensive with large knowledge bases
- **Swarm Coordinator**: Network intensive with many agents

## Scalability Features

### Distributed Coordination

```json
{
  "scalability_mechanisms": {
    "horizontal_scaling": {
      "innovator": "Parallel exploration paths",
      "memory_manager": "Distributed storage shards",
      "swarm_coordinator": "Hierarchical coordination"
    },
    "adaptive_behavior": {
      "load_balancing": "Dynamic resource allocation",
      "fault_tolerance": "Graceful degradation",
      "self_organization": "Emergent optimization"
    }
  }
}
```

## Quick Reference

| Mode | Primary Focus | Key Mechanisms | Typical Scale |
|------|--------------|----------------|---------------|
| Innovator | Creative Solutions | Pattern Synthesis | 1-10 agents |
| Memory Manager | Knowledge Coordination | Distributed State | System-wide |
| Swarm Coordinator | Multi-Agent Orchestration | Consensus Protocols | 10-1000 agents |

## Integration Points

The Coordination Group integrates with:
- **Analysis-Testing Group**: Validate innovations and coordination
- **Design-Documentation Group**: Implement and document breakthroughs
- **Development Modes**: Execute coordinated implementations

Remember: These modes enable the most advanced SPARC capabilities. Use them for complex, distributed challenges requiring creative solutions and sophisticated coordination.