# Quick Reference Guide - RUST-SS Optimization Patterns

## Pattern Selection Matrix

| Scenario | Circuit Breaker | Rate Limiter | Connection Pool | Load Balancer |
|----------|----------------|--------------|-----------------|---------------|
| **External API calls** | ✅ Essential | ✅ Essential | ✅ Essential | ⚡ Optional |
| **Database operations** | ✅ Essential | ⚡ Optional | ✅ Essential | ✅ Essential |
| **Agent-to-agent comm** | ✅ Essential | ✅ Essential | ⚡ Optional | ✅ Essential |
| **Message queues** | ⚡ Optional | ✅ Essential | ✅ Essential | ⚡ Optional |
| **Cache access** | ⚡ Optional | ⚡ Optional | ✅ Essential | ✅ Essential |
| **Batch processing** | ⚡ Optional | ✅ Essential | ⚡ Optional | ⚡ Optional |
| **Real-time streams** | ✅ Essential | ✅ Essential | ✅ Essential | ✅ Essential |

✅ Essential | ⚡ Optional/Recommended

---

## Performance Targets Cheat Sheet

### Circuit Breaker
```yaml
latency_targets:
  state_check: <100ns
  permit_acquisition: <500ns
  total_overhead: <2μs
  
capacity_targets:
  requests_per_second: 1M+
  concurrent_requests: 10K+
  memory_per_breaker: <1KB
```

### Rate Limiter
```yaml
latency_targets:
  token_acquisition: <50ns
  bucket_check: <25ns
  window_update: <100ns
  
accuracy_targets:
  rate_enforcement: ±5%
  burst_handling: 2x base rate
  distributed_sync: <50ms
```

### Connection Pool
```yaml
latency_targets:
  connection_acquisition: <1ms
  validation_overhead: <50μs
  pool_wait_time: <100μs
  
efficiency_targets:
  connection_reuse: >90%
  pool_utilization: 70-90%
  health_check_success: >99%
```

### Load Balancer
```yaml
latency_targets:
  target_selection: <100μs
  health_check: <1ms
  failover_time: <100ms
  
distribution_targets:
  variance: <5%
  target_availability: >99.9%
  false_positive_rate: <0.01%
```

---

## Configuration Quick Start

### Minimal Circuit Breaker
```yaml
circuit_breaker:
  failure_threshold: 5
  reset_timeout_seconds: 30
  failure_rate_threshold: 0.5
```

### Minimal Rate Limiter
```yaml
rate_limiter:
  algorithm: token_bucket
  rate_per_second: 1000
  burst_size: 5000
```

### Minimal Connection Pool
```yaml
connection_pool:
  min_connections: 5
  max_connections: 50
  connection_timeout_seconds: 5
```

### Minimal Load Balancer
```yaml
load_balancer:
  algorithm: round_robin
  health_check_interval_seconds: 10
  unhealthy_threshold: 3
```

---

## Algorithm Selection Guide

### Rate Limiting Algorithms
| Algorithm | Use Case | Pros | Cons |
|-----------|----------|------|------|
| **Token Bucket** | General purpose | Burst handling, Simple | Memory per bucket |
| **Sliding Window** | Accurate rate enforcement | Precise, Fair | More complex |
| **Fixed Window** | Simple quotas | Very simple | Edge spikes |
| **Leaky Bucket** | Smooth output | No bursts | No burst capacity |

### Load Balancing Algorithms
| Algorithm | Use Case | Complexity | Performance |
|-----------|----------|------------|-------------|
| **Round Robin** | Equal capacity targets | O(1) | 50ns |
| **Weighted RR** | Different capacities | O(1) | 80ns |
| **Least Conn** | Long connections | O(n) | 200ns |
| **Power of Two** | General purpose | O(1) | 150ns |
| **Consistent Hash** | Session affinity | O(log n) | 300ns |

---

## Integration Patterns Quick Reference

### Failure Escalation Order
1. **Rate Limiting** → Throttle excess requests
2. **Connection Pool** → Queue when pool busy
3. **Circuit Breaker** → Fail fast on errors
4. **Load Balancer** → Remove unhealthy targets

### Recovery Coordination Order
1. **Load Balancer** → Detect healthy targets
2. **Circuit Breaker** → Enter half-open state
3. **Connection Pool** → Validate connections
4. **Rate Limiter** → Gradually increase rate

---

## Common Configurations by Environment

### Development
```yaml
optimization_patterns:
  circuit_breaker:
    failure_threshold: 20
    reset_timeout_seconds: 5
  rate_limiter:
    rate_per_second: 10000
  connection_pool:
    max_connections: 20
  load_balancer:
    health_check_interval_seconds: 30
```

### Staging
```yaml
optimization_patterns:
  circuit_breaker:
    failure_threshold: 10
    reset_timeout_seconds: 15
  rate_limiter:
    rate_per_second: 5000
  connection_pool:
    max_connections: 50
  load_balancer:
    health_check_interval_seconds: 15
```

### Production
```yaml
optimization_patterns:
  circuit_breaker:
    failure_threshold: 5
    reset_timeout_seconds: 30
  rate_limiter:
    rate_per_second: 1000
  connection_pool:
    max_connections: 100
  load_balancer:
    health_check_interval_seconds: 10
```

---

## Monitoring Checklist

### Circuit Breaker Metrics
- [ ] State transitions per minute
- [ ] Request rejection rate
- [ ] Recovery success rate
- [ ] False positive rate

### Rate Limiter Metrics
- [ ] Token consumption rate
- [ ] Request rejection rate
- [ ] Burst utilization
- [ ] Rate accuracy

### Connection Pool Metrics
- [ ] Pool utilization percentage
- [ ] Connection wait time
- [ ] Connection lifetime
- [ ] Health check results

### Load Balancer Metrics
- [ ] Request distribution variance
- [ ] Target health status
- [ ] Failover frequency
- [ ] Selection latency

---

## Emergency Response Procedures

### Circuit Breaker Open
1. Check error logs for root cause
2. Verify downstream service health
3. Consider manual reset if false positive
4. Monitor half-open transitions

### Rate Limit Exceeded
1. Check for traffic spikes
2. Verify client behavior
3. Consider temporary limit increase
4. Implement backoff strategy

### Pool Exhaustion
1. Check for connection leaks
2. Verify connection timeouts
3. Consider pool size increase
4. Monitor connection lifecycle

### No Healthy Targets
1. Check all target health
2. Verify health check config
3. Check network connectivity
4. Review recent deployments

---

## Performance Tuning Quick Wins

### Reduce Latency
1. Enable lock-free state checks
2. Use thread-local token caches
3. Pre-warm connection pools
4. Cache target health status

### Increase Throughput
1. Batch metric updates
2. Use power-of-two selection
3. Enable connection multiplexing
4. Implement zero-copy buffers

### Reduce Memory
1. Share breaker instances
2. Use compact state representations
3. Limit connection pool size
4. Implement connection recycling

### Improve Reliability
1. Add jitter to recovery
2. Implement gradual recovery
3. Use exponential backoff
4. Enable health propagation

---

## Command Reference

### Testing Commands
```bash
# Test circuit breaker
curl -X POST /admin/circuit-breaker/test-failure -d '{"service":"api"}'

# Test rate limiter
ab -n 10000 -c 100 http://localhost:8080/api/test

# Test connection pool
pgbench -c 100 -j 10 -t 1000 postgresql://localhost/test

# Test load balancer
wrk -t10 -c100 -d30s --latency http://localhost:8080
```

### Monitoring Commands
```bash
# View pattern metrics
curl http://localhost:9090/metrics | grep optimization_pattern

# Check health status
curl http://localhost:8080/health/patterns

# View configuration
curl http://localhost:8080/admin/config/patterns
```

### Management Commands
```bash
# Reset circuit breaker
curl -X POST /admin/circuit-breaker/reset -d '{"service":"api"}'

# Adjust rate limit
curl -X PUT /admin/rate-limiter/limit -d '{"rate":2000}'

# Drain connection pool
curl -X POST /admin/pool/drain -d '{"pool":"database"}'

# Update load balancer
curl -X PUT /admin/balancer/targets -d '{"add":["server4:8080"]}'
```