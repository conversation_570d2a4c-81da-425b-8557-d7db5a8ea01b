# CLAUDE-INTEGRATION.md - Integration Patterns and Protocols Configuration

## Overview

This consolidated configuration file contains all integration patterns, protocols, and external system connectivity configurations for the claude-code-flow system. It covers MCP protocol integration, batch operations, external systems, and enterprise patterns.

## Table of Contents

1. [Integration Architecture Overview](#integration-architecture-overview)
2. [MCP Protocol Integration](#mcp-protocol-integration)
3. [Batch Operations and Tools](#batch-operations-and-tools)
4. [External System Integration](#external-system-integration)
5. [Data Synchronization](#data-synchronization)
6. [Communication Protocols](#communication-protocols)
7. [Enterprise Integration Patterns](#enterprise-integration-patterns)
8. [Optimization Patterns](#optimization-patterns)
9. [Best Practices](#best-practices)

---

## Integration Architecture Overview

### Design Principles
- **Protocol Agnostic**: Support multiple communication protocols
- **Loosely Coupled**: Services interact through well-defined interfaces
- **Fault Tolerant**: Handle failures gracefully with recovery
- **Performance Optimized**: Batch operations and connection pooling
- **Security First**: Authentication and authorization at every layer

### Integration Categories
1. **Protocol Integration**: MCP, REST, GraphQL, gRPC, WebSocket
2. **Batch Operations**: TodoWrite, Task coordination, parallel execution
3. **External Systems**: Third-party APIs, databases, cloud services
4. **Data Synchronization**: Real-time sync, eventual consistency
5. **Enterprise Patterns**: ESB, message queuing, event streaming

---

## MCP Protocol Integration

### Overview
The Model Context Protocol (MCP) integration enables standardized agent-to-agent communication, tool registration, and capability negotiation.

### Architecture Components
```rust
pub struct MCPArchitecture {
    server: MCPServer,
    transport: Box<dyn Transport>,
    tool_registry: Arc<RwLock<ToolRegistry>>,
    session_manager: Arc<SessionManager>,
    capability_manager: CapabilityManager,
}
```

### Transport Mechanisms

#### Supported Transports
- **stdio**: Standard input/output for subprocess communication
- **HTTP**: RESTful API with JSON-RPC 2.0 protocol
- **Streamable HTTP**: Enhanced HTTP with Server-Sent Events
- **WebSocket**: Real-time bidirectional communication

#### Transport Implementation
```rust
#[async_trait]
pub trait Transport: Send + Sync {
    async fn connect(&mut self) -> Result<(), TransportError>;
    async fn send(&self, message: &[u8]) -> Result<(), TransportError>;
    async fn receive(&mut self) -> Result<Vec<u8>, TransportError>;
    async fn close(&mut self) -> Result<(), TransportError>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportType {
    Stdio,
    Http { host: String, port: u16 },
    StreamableHttp { endpoint: String },
    WebSocket { url: String },
}
```

### Tool Registration System

#### Dynamic Tool Registration
```rust
#[async_trait]
pub trait ToolHandler: Send + Sync {
    async fn execute(
        &self,
        params: Map<String, Value>,
        context: &ToolContext,
    ) -> Result<ToolResult, ToolError>;
    
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn input_schema(&self) -> &JSONSchema;
    fn output_schema(&self) -> Option<&JSONSchema>;
}

pub struct ToolRegistry {
    tools: HashMap<String, Arc<ToolRegistration>>,
    handlers: HashMap<String, Arc<dyn ToolHandler>>,
    capabilities: HashMap<String, Vec<Capability>>,
}
```

#### Tool Registration Flow
1. Tool declares capabilities and schema
2. Registry validates schema compliance
3. Security policies are applied
4. Tool is made available for discovery
5. Clients can query available tools

### Capability Negotiation

#### Protocol Negotiation
```typescript
interface CapabilityNegotiation {
  // Client declares supported features
  clientCapabilities: {
    protocolVersion: string;
    supportedTransports: TransportType[];
    features: string[];
  };
  
  // Server responds with available features
  serverCapabilities: {
    protocolVersion: string;
    availableTransports: TransportType[];
    features: string[];
    tools: ToolDescriptor[];
  };
  
  // Negotiated capabilities
  negotiatedCapabilities: {
    transport: TransportType;
    features: string[];
    tools: string[];
  };
}
```

### Security Framework

#### Authentication Layers
- **Token-based**: JWT tokens with role-based permissions
- **Mutual TLS**: Certificate-based authentication
- **API Key Management**: Secure key rotation
- **Session Management**: Secure session lifecycle

#### Authorization Patterns
```rust
pub struct SecurityPolicy {
    pub require_auth: bool,
    pub allowed_roles: Vec<String>,
    pub rate_limit: Option<RateLimit>,
    pub audit_logging: bool,
}

pub struct RateLimit {
    pub requests_per_minute: u32,
    pub burst_size: u32,
}
```

### Error Handling

#### MCP Error Types
```rust
#[derive(Error, Debug)]
pub enum McpError {
    #[error("Protocol error: {0}")]
    ProtocolError(ProtocolError),
    
    #[error("Transport error: {0}")]
    TransportError(#[from] TransportError),
    
    #[error("Authentication error: {0}")]
    AuthenticationError(AuthError),
    
    #[error("Tool execution error: {0}")]
    ToolError(#[from] ToolError),
    
    #[error("Timeout after {timeout:?}")]
    Timeout { timeout: Duration },
}
```

---

## Batch Operations and Tools

### TodoWrite Coordination Pattern

#### Overview
Complex task breakdown with dependencies, status tracking, and agent assignment.

#### TodoWrite Structure
```javascript
TodoWrite([
  {
    id: "architecture_design",
    content: "Design system architecture and component interfaces",
    status: "pending",
    priority: "high",
    dependencies: [],
    estimatedTime: "60min",
    assignedAgent: "architect"
  },
  {
    id: "frontend_development", 
    content: "Develop React components and user interface",
    status: "pending",
    priority: "medium",
    dependencies: ["architecture_design"],
    estimatedTime: "120min",
    assignedAgent: "frontend_team"
  }
]);
```

#### Rust Implementation
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Todo {
    pub id: String,
    pub content: String,
    pub status: TodoStatus,
    pub priority: Priority,
    pub dependencies: Vec<String>,
    pub estimated_time: String,
    pub assigned_agent: String,
    pub metadata: HashMap<String, Value>,
}

pub struct TodoCoordinator {
    todos: Vec<Todo>,
    dependency_graph: Graph<String, ()>,
    status_tracker: StatusTracker,
}

impl TodoCoordinator {
    pub fn write_todos(&mut self, todos: Vec<Todo>) -> Result<(), TodoError> {
        self.validate_dependencies(&todos)?;
        self.build_dependency_graph(&todos)?;
        self.check_cycles()?;
        self.todos = todos;
        Ok(())
    }
    
    pub fn get_ready_tasks(&self) -> Vec<&Todo> {
        self.todos.iter()
            .filter(|todo| {
                todo.status == TodoStatus::Pending &&
                self.dependencies_satisfied(todo)
            })
            .collect()
    }
}
```

### Task Spawning and Parallel Execution

#### Multi-Agent Coordination
```typescript
const agentIds = await coordinator.launchParallelAgents([
  {
    agentType: 'researcher',
    objective: 'Research microservices patterns',
    mode: 'researcher',
    memoryKey: 'microservices_research',
    batchOptimized: true
  },
  {
    agentType: 'architect',
    objective: 'Design system architecture',
    mode: 'architect', 
    memoryKey: 'system_architecture',
    batchOptimized: true
  }
], context);
```

#### Rust Parallel Execution
```rust
pub struct BatchCoordinator {
    agent_pool: Arc<RwLock<AgentPool>>,
    concurrency_limit: Arc<Semaphore>,
    memory_store: Arc<MemoryStore>,
}

impl BatchCoordinator {
    pub async fn launch_parallel_agents(
        &self,
        configs: Vec<AgentConfig>,
    ) -> Result<Vec<String>, CoordinatorError> {
        let mut join_set = JoinSet::new();
        
        for config in configs {
            let permit = self.concurrency_limit.clone().acquire_owned().await?;
            let agent_id = Uuid::new_v4().to_string();
            
            join_set.spawn(async move {
                let _permit = permit; // Hold until complete
                Self::spawn_agent(agent_id.clone(), config).await
            });
        }
        
        // Wait for all agents
        while let Some(result) = join_set.join_next().await {
            result??;
        }
        
        Ok(agent_ids)
    }
}
```

### Batch Operations

#### Operation Types
```typescript
const results = await coordinator.coordinateBatchOperations([
  {
    type: 'read',
    targets: ['src/**/*.ts'],
    configuration: { pattern: 'class.*{' }
  },
  {
    type: 'search',
    targets: ['docs/**/*.md'],
    configuration: { term: 'API documentation' }
  },
  {
    type: 'analyze',
    targets: ['package.json', 'tsconfig.json'],
    configuration: { focus: 'dependencies' }
  }
], context);
```

#### Batch Processing Pipeline
```rust
pub struct BatchOperationExecutor {
    file_reader: Arc<FileReader>,
    search_engine: Arc<SearchEngine>,
    analyzer: Arc<Analyzer>,
    pipeline: BatchPipeline,
}

impl BatchOperationExecutor {
    pub async fn execute_batch(
        &self,
        operations: Vec<BatchOperation>,
    ) -> Result<Vec<OperationResult>, BatchError> {
        stream::iter(operations)
            .map(|op| self.execute_operation(op))
            .buffer_unordered(4) // Concurrent execution
            .collect::<Vec<_>>()
            .await
            .into_iter()
            .collect()
    }
}
```

### Work Stealing Pattern

#### Dynamic Load Balancing
```rust
pub struct WorkStealingExecutor {
    workers: Vec<Worker>,
    global_queue: Arc<SegQueue<Task>>,
    local_queues: Vec<Arc<SegQueue<Task>>>,
}

impl WorkStealingExecutor {
    pub async fn execute_tasks(&self, tasks: Vec<Task>) {
        // Distribute tasks
        for (i, task) in tasks.into_iter().enumerate() {
            self.local_queues[i % self.workers.len()].push(task);
        }
        
        // Start workers with work stealing
        let handles: Vec<_> = self.workers
            .iter()
            .enumerate()
            .map(|(id, worker)| {
                let local_queue = self.local_queues[id].clone();
                let steal_queues = self.get_steal_queues(id);
                
                tokio::spawn(async move {
                    worker.run_with_stealing(local_queue, steal_queues).await
                })
            })
            .collect();
        
        futures::future::join_all(handles).await;
    }
}
```

---

## External System Integration

### Integration Patterns

#### Adapter Pattern
```rust
pub trait ExternalSystemAdapter: Send + Sync {
    async fn connect(&mut self) -> Result<(), IntegrationError>;
    async fn execute(&self, operation: Operation) -> Result<Response, IntegrationError>;
    async fn disconnect(&mut self) -> Result<(), IntegrationError>;
    fn health_check(&self) -> HealthStatus;
}

pub struct IntegrationHub {
    adapters: HashMap<String, Box<dyn ExternalSystemAdapter>>,
    circuit_breakers: HashMap<String, CircuitBreaker>,
    retry_policies: HashMap<String, RetryPolicy>,
}
```

#### Common Integrations
1. **Cloud Providers**
   - AWS SDK integration
   - Azure services
   - Google Cloud Platform
   - Custom cloud APIs

2. **Databases**
   - PostgreSQL
   - MongoDB
   - Redis
   - Elasticsearch

3. **Message Systems**
   - Kafka
   - RabbitMQ
   - AWS SQS
   - NATS

4. **Third-party APIs**
   - GitHub
   - Slack
   - JIRA
   - Custom webhooks

### Connection Management

#### Connection Pooling
```rust
pub struct ConnectionPool<T: Connection> {
    available: Arc<Mutex<Vec<T>>>,
    in_use: Arc<Mutex<HashSet<Uuid>>>,
    config: PoolConfig,
}

pub struct PoolConfig {
    pub min_connections: usize,
    pub max_connections: usize,
    pub connection_timeout: Duration,
    pub idle_timeout: Duration,
    pub validation_interval: Duration,
}

impl<T: Connection> ConnectionPool<T> {
    pub async fn acquire(&self) -> Result<PooledConnection<T>, PoolError> {
        // Try to get available connection
        if let Some(conn) = self.try_acquire_available().await? {
            return Ok(conn);
        }
        
        // Create new if under limit
        if self.can_create_new().await {
            return self.create_new_connection().await;
        }
        
        // Wait for available connection
        self.wait_for_available().await
    }
}
```

### Error Handling and Recovery

#### Circuit Breaker Pattern
```rust
pub struct CircuitBreaker {
    state: Arc<RwLock<CircuitState>>,
    config: CircuitConfig,
    metrics: Arc<CircuitMetrics>,
}

pub struct CircuitConfig {
    pub failure_threshold: u32,
    pub success_threshold: u32,
    pub timeout: Duration,
    pub half_open_max_calls: u32,
}

impl CircuitBreaker {
    pub async fn call<F, T>(&self, operation: F) -> Result<T, CircuitError>
    where
        F: Future<Output = Result<T, Error>>,
    {
        let state = self.state.read().await;
        
        match *state {
            CircuitState::Open => {
                if self.should_attempt_reset().await {
                    drop(state);
                    self.transition_to_half_open().await;
                } else {
                    return Err(CircuitError::OpenCircuit);
                }
            }
            CircuitState::HalfOpen => {
                if self.half_open_calls >= self.config.half_open_max_calls {
                    return Err(CircuitError::HalfOpenLimit);
                }
            }
            CircuitState::Closed => {}
        }
        
        // Execute operation
        match operation.await {
            Ok(result) => {
                self.record_success().await;
                Ok(result)
            }
            Err(error) => {
                self.record_failure().await;
                Err(CircuitError::OperationFailed(error))
            }
        }
    }
}
```

---

## Data Synchronization

### Synchronization Strategies

#### Event-Driven Sync
```rust
pub struct EventDrivenSync {
    event_bus: Arc<EventBus>,
    sync_handlers: HashMap<String, Box<dyn SyncHandler>>,
    conflict_resolver: ConflictResolver,
}

#[async_trait]
pub trait SyncHandler: Send + Sync {
    async fn handle_create(&self, entity: Entity) -> Result<(), SyncError>;
    async fn handle_update(&self, entity: Entity, changes: Changes) -> Result<(), SyncError>;
    async fn handle_delete(&self, entity_id: String) -> Result<(), SyncError>;
    async fn resolve_conflict(&self, local: Entity, remote: Entity) -> Result<Entity, SyncError>;
}
```

#### Bidirectional Sync
```rust
pub struct BidirectionalSync {
    local_store: Arc<dyn DataStore>,
    remote_store: Arc<dyn DataStore>,
    sync_log: SyncLog,
    conflict_strategy: ConflictStrategy,
}

impl BidirectionalSync {
    pub async fn sync(&self) -> Result<SyncResult, SyncError> {
        // Get changes from both sides
        let local_changes = self.local_store.get_changes_since(self.sync_log.last_sync).await?;
        let remote_changes = self.remote_store.get_changes_since(self.sync_log.last_sync).await?;
        
        // Detect conflicts
        let conflicts = self.detect_conflicts(&local_changes, &remote_changes);
        
        // Resolve conflicts
        let resolutions = self.resolve_conflicts(conflicts).await?;
        
        // Apply changes
        self.apply_changes(local_changes, remote_changes, resolutions).await?;
        
        // Update sync log
        self.sync_log.record_sync(Utc::now()).await?;
        
        Ok(SyncResult {
            local_applied: local_changes.len(),
            remote_applied: remote_changes.len(),
            conflicts_resolved: resolutions.len(),
        })
    }
}
```

### Conflict Resolution

#### Conflict Detection
```rust
pub struct ConflictDetector {
    comparison_rules: Vec<Box<dyn ComparisonRule>>,
    version_tracker: VersionTracker,
}

impl ConflictDetector {
    pub fn detect_conflicts(
        &self,
        local: &Entity,
        remote: &Entity,
    ) -> Option<Conflict> {
        // Version-based detection
        if let Some(conflict) = self.version_tracker.check_versions(local, remote) {
            return Some(conflict);
        }
        
        // Field-level detection
        for rule in &self.comparison_rules {
            if let Some(conflict) = rule.compare(local, remote) {
                return Some(conflict);
            }
        }
        
        None
    }
}
```

#### Resolution Strategies
```rust
pub enum ConflictStrategy {
    LastWriteWins,
    FirstWriteWins,
    MergeFields,
    Manual,
    Custom(Box<dyn ConflictResolver>),
}

pub struct ConflictResolver {
    strategy: ConflictStrategy,
    merge_rules: Vec<MergeRule>,
}

impl ConflictResolver {
    pub async fn resolve(
        &self,
        conflict: Conflict,
    ) -> Result<Resolution, ResolutionError> {
        match &self.strategy {
            ConflictStrategy::LastWriteWins => {
                Ok(Resolution::UseRemote(conflict.remote))
            }
            ConflictStrategy::MergeFields => {
                self.merge_fields(conflict.local, conflict.remote).await
            }
            ConflictStrategy::Custom(resolver) => {
                resolver.resolve(conflict).await
            }
            _ => Err(ResolutionError::UnsupportedStrategy),
        }
    }
}
```

---

## Communication Protocols

### Protocol Support Matrix

| Protocol | Use Case | Performance | Reliability |
|----------|----------|-------------|-------------|
| HTTP/REST | External APIs | Medium | High |
| gRPC | Service-to-service | High | High |
| WebSocket | Real-time updates | High | Medium |
| NATS | Event streaming | Very High | High |
| GraphQL | Flexible queries | Medium | High |

### Protocol Abstraction

#### Unified Protocol Interface
```rust
#[async_trait]
pub trait ProtocolHandler: Send + Sync {
    type Request;
    type Response;
    type Error;
    
    async fn send(&self, request: Self::Request) -> Result<Self::Response, Self::Error>;
    async fn receive(&mut self) -> Result<Self::Request, Self::Error>;
    fn protocol_name(&self) -> &str;
    fn supports_streaming(&self) -> bool;
}

pub struct ProtocolRouter {
    handlers: HashMap<String, Box<dyn ProtocolHandler>>,
    default_protocol: String,
}

impl ProtocolRouter {
    pub async fn route(
        &self,
        protocol: Option<&str>,
        request: Request,
    ) -> Result<Response, RouterError> {
        let protocol = protocol.unwrap_or(&self.default_protocol);
        
        let handler = self.handlers
            .get(protocol)
            .ok_or(RouterError::UnknownProtocol)?;
            
        handler.send(request).await
            .map_err(|e| RouterError::HandlerError(e))
    }
}
```

### Message Formats

#### Protocol Buffers
```protobuf
syntax = "proto3";

message TaskRequest {
  string id = 1;
  string type = 2;
  google.protobuf.Any payload = 3;
  map<string, string> metadata = 4;
  int64 timestamp = 5;
}

message TaskResponse {
  string id = 1;
  bool success = 2;
  google.protobuf.Any result = 3;
  repeated Error errors = 4;
}

message Error {
  string code = 1;
  string message = 2;
  map<string, string> details = 3;
}
```

#### JSON Schema
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "id": { "type": "string", "format": "uuid" },
    "type": { "type": "string", "enum": ["task", "event", "command"] },
    "payload": { "type": "object" },
    "metadata": { 
      "type": "object",
      "additionalProperties": { "type": "string" }
    },
    "timestamp": { "type": "integer" }
  },
  "required": ["id", "type", "payload"]
}
```

---

## Enterprise Integration Patterns

### Enterprise Service Bus (ESB)

#### Message Routing
```rust
pub struct EnterpriseServiceBus {
    routes: HashMap<String, Route>,
    transformers: HashMap<String, Box<dyn MessageTransformer>>,
    enrichers: HashMap<String, Box<dyn MessageEnricher>>,
    filters: Vec<Box<dyn MessageFilter>>,
}

pub struct Route {
    pub source: String,
    pub destination: String,
    pub conditions: Vec<RouteCondition>,
    pub transformations: Vec<String>,
    pub enrichments: Vec<String>,
}

impl EnterpriseServiceBus {
    pub async fn route_message(
        &self,
        message: Message,
    ) -> Result<Vec<RoutedMessage>, RoutingError> {
        // Apply filters
        if !self.pass_filters(&message).await? {
            return Ok(vec![]);
        }
        
        // Find matching routes
        let routes = self.find_matching_routes(&message);
        
        // Process each route
        let mut results = Vec::new();
        for route in routes {
            let mut msg = message.clone();
            
            // Apply transformations
            for transform_id in &route.transformations {
                msg = self.transform_message(transform_id, msg).await?;
            }
            
            // Apply enrichments
            for enrich_id in &route.enrichments {
                msg = self.enrich_message(enrich_id, msg).await?;
            }
            
            results.push(RoutedMessage {
                destination: route.destination.clone(),
                message: msg,
            });
        }
        
        Ok(results)
    }
}
```

### Message Queue Integration

#### Queue Abstraction
```rust
#[async_trait]
pub trait MessageQueue: Send + Sync {
    async fn publish(&self, topic: &str, message: Message) -> Result<(), QueueError>;
    async fn subscribe(&self, topic: &str) -> Result<Box<dyn Stream<Item = Message>>, QueueError>;
    async fn acknowledge(&self, message_id: &str) -> Result<(), QueueError>;
    async fn nack(&self, message_id: &str, requeue: bool) -> Result<(), QueueError>;
}

pub struct QueueManager {
    queues: HashMap<String, Box<dyn MessageQueue>>,
    dead_letter_queue: Box<dyn MessageQueue>,
    retry_policy: RetryPolicy,
}

impl QueueManager {
    pub async fn process_messages<F>(
        &self,
        queue_name: &str,
        processor: F,
    ) -> Result<(), ProcessError>
    where
        F: Fn(Message) -> BoxFuture<'static, Result<(), ProcessError>>,
    {
        let queue = self.queues.get(queue_name)
            .ok_or(ProcessError::QueueNotFound)?;
            
        let mut stream = queue.subscribe("*").await?;
        
        while let Some(message) = stream.next().await {
            match processor(message.clone()).await {
                Ok(_) => {
                    queue.acknowledge(&message.id).await?;
                }
                Err(e) if self.should_retry(&e) => {
                    queue.nack(&message.id, true).await?;
                }
                Err(_) => {
                    // Send to dead letter queue
                    self.dead_letter_queue.publish("dlq", message).await?;
                    queue.acknowledge(&message.id).await?;
                }
            }
        }
        
        Ok(())
    }
}
```

### Event Streaming

#### Stream Processing
```rust
pub struct EventStreamProcessor {
    source: Box<dyn EventSource>,
    processors: Vec<Box<dyn StreamProcessor>>,
    sink: Box<dyn EventSink>,
    checkpoint_store: CheckpointStore,
}

impl EventStreamProcessor {
    pub async fn process_stream(&self) -> Result<(), StreamError> {
        let mut checkpoint = self.checkpoint_store.load().await?;
        let mut stream = self.source.stream_from(checkpoint).await?;
        
        while let Some(event) = stream.next().await {
            // Process through pipeline
            let mut processed = event.clone();
            for processor in &self.processors {
                processed = processor.process(processed).await?;
            }
            
            // Write to sink
            self.sink.write(processed).await?;
            
            // Update checkpoint
            checkpoint = event.offset;
            if event.sequence % 100 == 0 {
                self.checkpoint_store.save(checkpoint).await?;
            }
        }
        
        Ok(())
    }
}
```

---

## Optimization Patterns

### Connection Pooling

#### Advanced Pool Management
```rust
pub struct AdvancedConnectionPool<T: Connection> {
    connections: Arc<Mutex<Vec<PooledConnection<T>>>>,
    stats: Arc<RwLock<PoolStatistics>>,
    health_checker: HealthChecker<T>,
    scaler: AutoScaler,
}

impl<T: Connection> AdvancedConnectionPool<T> {
    pub async fn acquire_with_priority(
        &self,
        priority: Priority,
    ) -> Result<PooledConnection<T>, PoolError> {
        // Priority queue for high-priority requests
        match priority {
            Priority::High => self.acquire_high_priority().await,
            Priority::Normal => self.acquire_normal().await,
            Priority::Low => self.acquire_with_timeout(Duration::from_secs(30)).await,
        }
    }
    
    pub async fn auto_scale(&self) {
        loop {
            let stats = self.stats.read().await;
            let scaling_decision = self.scaler.decide(&stats);
            
            match scaling_decision {
                ScaleAction::Increase(n) => {
                    for _ in 0..n {
                        if let Ok(conn) = T::connect().await {
                            self.add_connection(conn).await;
                        }
                    }
                }
                ScaleAction::Decrease(n) => {
                    self.remove_idle_connections(n).await;
                }
                ScaleAction::None => {}
            }
            
            tokio::time::sleep(Duration::from_secs(10)).await;
        }
    }
}
```

### Caching Strategies

#### Multi-Level Cache
```rust
pub struct MultiLevelCache {
    l1_memory: Arc<MemoryCache>,
    l2_redis: Arc<RedisCache>,
    l3_persistent: Arc<PersistentCache>,
    stats: Arc<CacheStatistics>,
}

impl MultiLevelCache {
    pub async fn get<T: DeserializeOwned>(&self, key: &str) -> Option<T> {
        // L1 check
        if let Some(value) = self.l1_memory.get(key).await {
            self.stats.record_hit(CacheLevel::L1).await;
            return Some(value);
        }
        
        // L2 check
        if let Some(value) = self.l2_redis.get(key).await {
            self.stats.record_hit(CacheLevel::L2).await;
            // Promote to L1
            self.l1_memory.set(key, &value, Duration::from_secs(300)).await;
            return Some(value);
        }
        
        // L3 check
        if let Some(value) = self.l3_persistent.get(key).await {
            self.stats.record_hit(CacheLevel::L3).await;
            // Promote to L1 and L2
            self.l2_redis.set(key, &value, Duration::from_secs(3600)).await;
            self.l1_memory.set(key, &value, Duration::from_secs(300)).await;
            return Some(value);
        }
        
        self.stats.record_miss().await;
        None
    }
}
```

### Batch Processing Optimization

#### Adaptive Batching
```rust
pub struct AdaptiveBatcher<T> {
    batch_size: Arc<AtomicUsize>,
    latency_target: Duration,
    throughput_monitor: ThroughputMonitor,
    items: Arc<Mutex<Vec<T>>>,
}

impl<T> AdaptiveBatcher<T> {
    pub async fn add(&self, item: T) -> Result<(), BatchError> {
        let mut items = self.items.lock().await;
        items.push(item);
        
        if items.len() >= self.batch_size.load(Ordering::Relaxed) {
            self.flush().await?;
        }
        
        Ok(())
    }
    
    pub async fn adaptive_flush(&self) {
        loop {
            // Monitor performance
            let metrics = self.throughput_monitor.get_metrics().await;
            
            // Adjust batch size based on latency
            if metrics.avg_latency > self.latency_target {
                // Reduce batch size
                self.batch_size.fetch_sub(10, Ordering::Relaxed);
            } else if metrics.avg_latency < self.latency_target * 0.8 {
                // Increase batch size
                self.batch_size.fetch_add(10, Ordering::Relaxed);
            }
            
            // Periodic flush
            tokio::time::sleep(Duration::from_millis(100)).await;
            self.flush().await.ok();
        }
    }
}
```

---

## Best Practices

### Integration Guidelines

1. **Protocol Selection**
   - Use gRPC for internal service communication
   - REST for external APIs
   - WebSocket for real-time updates
   - Message queues for async processing

2. **Error Handling**
   - Implement circuit breakers for external calls
   - Use exponential backoff for retries
   - Log all integration failures
   - Provide graceful degradation

3. **Performance**
   - Always use connection pooling
   - Implement caching at multiple levels
   - Batch operations when possible
   - Monitor and optimize latency

4. **Security**
   - Authenticate all external connections
   - Use TLS for data in transit
   - Rotate credentials regularly
   - Audit all integration access

5. **Monitoring**
   - Track integration health metrics
   - Alert on connection failures
   - Monitor data flow volumes
   - Log integration events

### Anti-Patterns to Avoid

1. **Chatty Interfaces**: Minimize round trips
2. **Synchronous Everything**: Use async where appropriate
3. **No Circuit Breakers**: Always protect against failures
4. **Hardcoded Endpoints**: Use configuration
5. **No Monitoring**: Always instrument integrations

### Testing Strategies

1. **Integration Tests**
   ```rust
   #[tokio::test]
   async fn test_external_api_integration() {
       let mock_server = MockServer::start().await;
       mock_server
           .mock("GET", "/api/data")
           .with_status(200)
           .with_body(r#"{"status": "ok"}"#)
           .create();
           
       let client = ExternalApiClient::new(&mock_server.url());
       let result = client.get_data().await.unwrap();
       
       assert_eq!(result.status, "ok");
   }
   ```

2. **Contract Testing**: Verify API contracts
3. **Chaos Testing**: Test failure scenarios
4. **Load Testing**: Verify scalability
5. **Security Testing**: Validate authentication

---

## Summary

This CLAUDE-INTEGRATION.md file consolidates all integration configurations including:
- Complete MCP protocol implementation
- Batch operations and coordination patterns
- External system integration strategies
- Data synchronization mechanisms
- Communication protocol abstractions
- Enterprise integration patterns
- Performance optimization techniques
- Comprehensive best practices

These integration patterns enable seamless connectivity between claude-code-flow components and external systems while maintaining performance, reliability, and security.