{"timestamp": "2025-07-01T19:38:39.160Z", "version": "1.0", "entries": [{"id": "entry_mcku1wal_vfw4g3qu5", "key": "test-key", "value": "This is a test of the memory system", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T17:59:58.653Z", "updatedAt": "2025-07-01T17:59:58.653Z", "lastAccessedAt": "2025-07-01T18:53:09.275Z", "version": 1, "size": 66, "compressed": false, "checksum": "144ea629003bb0d4550d9f631bdab64acbe12524bfc9e779eb62b28cdb93fb3d", "references": [], "dependencies": []}, {"id": "entry_mckv2zgq_dgg9agbvs", "key": "test-key", "value": "Claude-flow is now running with MCP integration", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T18:28:49.034Z", "updatedAt": "2025-07-01T18:28:49.034Z", "lastAccessedAt": "2025-07-01T18:53:09.275Z", "version": 1, "size": 78, "compressed": false, "checksum": "194b0ab77ccea9aa3196490d4d5d05edded0301620446173cc6d6a36ab096b03", "references": [], "dependencies": []}, {"id": "entry_mckvxnqp_naj4tusym", "key": "test-connection", "value": "MCP integration test successful at Tue Jul  1 14:52:39 EDT 2025", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T18:52:40.177Z", "updatedAt": "2025-07-01T18:52:40.177Z", "lastAccessedAt": "2025-07-01T18:56:57.821Z", "version": 1, "size": 94, "compressed": false, "checksum": "c498a772885764517dba726939293562ca70d958b53dda1a2908955a03c9d738", "references": [], "dependencies": []}, {"id": "entry_mckvy1ah_n925041ah", "key": "test-config", "value": "{\\\"mcp_port\\\": 3001, \\\"transport\\\": \\\"stdio\\\", \\\"status\\\": \\\"testing\\\"}", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T18:52:57.737Z", "updatedAt": "2025-07-01T18:52:57.737Z", "lastAccessedAt": "2025-07-01T18:53:09.275Z", "version": 1, "size": 122, "compressed": false, "checksum": "6830225c3607083bf95c0767f04e272ec5da1ec012c0a098783acb052662aed0", "references": [], "dependencies": []}], "statistics": {"overview": {"totalEntries": 4, "totalSize": 360, "compressedEntries": 0, "compressionRatio": 1, "indexSize": 200, "memoryUsage": 9226464, "diskUsage": 0}, "distribution": {"byNamespace": {"default": {"count": 4, "size": 360}}, "byType": {"string": {"count": 4, "size": 360}}, "byOwner": {"system": {"count": 4, "size": 360}}, "byAccessLevel": {"shared": {"count": 4, "size": 360}}}, "temporal": {"entriesCreatedLast24h": 4, "entriesUpdatedLast24h": 4, "entriesAccessedLast24h": 4, "oldestEntry": "2025-07-01T17:59:58.653Z", "newestEntry": "2025-07-01T18:52:57.737Z"}, "performance": {"averageQueryTime": 0, "averageWriteTime": 0, "cacheHitRatio": 0, "indexEfficiency": 0.95}, "health": {"expiredEntries": 0, "orphanedReferences": 0, "duplicateKeys": 1, "corruptedEntries": 0, "recommendedCleanup": false}, "optimization": {"suggestions": ["1 duplicate keys found"], "potentialSavings": {"compression": 0, "cleanup": 0, "deduplication": 78}, "indexOptimization": ["Consider periodic index rebuilding for optimal performance"]}}}