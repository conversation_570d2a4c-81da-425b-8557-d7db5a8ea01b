#!/bin/bash
# Claude-Flow Swarm Mode with UI Wrapper
# This script wraps the enhanced swarm demo with blessed UI support

# Find deno in the path
DENO_PATH=$(which deno 2>/dev/null || echo "/home/<USER>/.deno/bin/deno")

if [ ! -x "$DENO_PATH" ]; then
    echo "Error: Deno is not installed or not in PATH"
    echo "Please install Deno first: https://deno.land/#installation"
    exit 1
fi

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SWARM_DEMO="$SCRIPT_DIR/../swarm-demo-enhanced.ts"

# Check if --ui flag is present, if not add it
if [[ "$*" != *"--ui"* ]]; then
    # Add --ui flag after the objective
    ARGS=()
    OBJECTIVE_DONE=false
    
    for arg in "$@"; do
        ARGS+=("$arg")
        if [[ ! "$OBJECTIVE_DONE" = true ]] && [[ ! "$arg" =~ ^- ]]; then
            OBJECTIVE_DONE=true
            # Next arg should be a flag, so we can insert --ui
            if [[ "${@:$((${#ARGS} + 1)):1}" =~ ^- ]] || [ $# -eq ${#ARGS[@]} ]; then
                ARGS+=("--ui")
            fi
        fi
    done
    
    # Run with --ui flag
    exec "$DENO_PATH" run --allow-all "$SWARM_DEMO" "${ARGS[@]}"
else
    # Run as-is
    exec "$DENO_PATH" run --allow-all "$SWARM_DEMO" "$@"
fi