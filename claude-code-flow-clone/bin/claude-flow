#!/bin/sh
# <PERSON><PERSON><PERSON> Smart Dispatcher - Detects and uses the best available runtime

VERSION="1.0.71"
SCRIPT_DIR=$(dirname "$0")
ROOT_DIR=$(cd "$SCRIPT_DIR/.." && pwd)

# Quick version check
for arg in "$@"; do
  if [ "$arg" = "--version" ] || [ "$arg" = "-v" ]; then
    echo "v$VERSION"
    exit 0
  fi
done

# Try Deno first, but check if it actually works
if command -v deno >/dev/null 2>&1 && [ -f "$ROOT_DIR/src/cli/main.ts" ]; then
  # Test if <PERSON><PERSON> can actually run the main file
  if deno check "$ROOT_DIR/src/cli/main.ts" >/dev/null 2>&1; then
    # Use Deno for full functionality
    exec deno run --allow-all "$ROOT_DIR/src/cli/main.ts" "$@"
  fi
fi

# Fall back to Node.js version
if command -v tsx >/dev/null 2>&1 && [ -f "$ROOT_DIR/src/cli/simple-cli.ts" ]; then
  # Use tsx for Node.js functionality
  exec tsx "$ROOT_DIR/src/cli/simple-cli.ts" "$@"
elif [ -f "$ROOT_DIR/src/cli/simple-cli.ts" ]; then
  # Try to use npx tsx as fallback
  exec npx tsx "$ROOT_DIR/src/cli/simple-cli.ts" "$@"
else
  # No runtime available, show help
  echo "🧠 Claude-Flow v$VERSION - Advanced AI Agent Orchestration System"
  echo ""
  echo "⚠️  No compatible runtime found."
  echo ""
  echo "For full functionality (recommended):"
  echo "  1. Install Deno: curl -fsSL https://deno.land/x/install/install.sh | sh"
  echo "  2. Run: claude-flow <command>"
  echo ""
  echo "For Node.js compatibility mode:"
  echo "  1. Install tsx: npm install -g tsx"
  echo "  2. Run: claude-flow <command>"
  echo ""
  echo "Or use npx directly:"
  echo "  npx tsx src/cli/simple-cli.ts <command>"
  echo ""
  echo "Documentation: https://github.com/ruvnet/claude-code-flow"
  exit 1
fi