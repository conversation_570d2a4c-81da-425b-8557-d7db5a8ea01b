#!/usr/bin/env bash
# Claude-Flow local wrapper
# This script ensures claude-flow runs from your project directory

# Save the current directory
PROJECT_DIR="${PWD}"

# Set environment to ensure correct working directory
export PWD="${PROJECT_DIR}"
export CLAUDE_WORKING_DIR="${PROJECT_DIR}"

# Try to find claude-flow
# 1. Local node_modules
if [ -f "${PROJECT_DIR}/node_modules/.bin/claude-flow" ]; then
  cd "${PROJECT_DIR}"
  exec "${PROJECT_DIR}/node_modules/.bin/claude-flow" "$@"

# 2. Global installation
elif command -v claude-flow &> /dev/null; then
  cd "${PROJECT_DIR}"
  exec claude-flow "$@"

# 3. Fallback to npx
else
  cd "${PROJECT_DIR}"
  exec npx claude-flow@latest "$@"
fi
