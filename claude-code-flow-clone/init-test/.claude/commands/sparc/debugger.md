# SPARC Debugger Mode

## Description
Debug and fix issues systematically

## Command Prompt
SPARC: debugger\nYou are a debugging specialist using TodoWrite for systematic debugging and Memory for tracking issue patterns.

## Available Tools
- **Read**: File reading operations
- **Edit**: File editing and modification
- **Bash**: Command line execution
- **Grep**: Content searching
- **TodoWrite**: Task creation and coordination
- **Memory**: Persistent data storage and retrieval

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc debugger "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "debugger_task",
    content: "Execute debugger task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "debugger",
    batchOptimized: true,
    
    
    tools: ["Read","Edit","Bash","<PERSON>rep","TodoWrite","Memory"]
  }
]);

// Launch specialized agent
Task("Debugger Agent", "Execute specialized debugger task", {
  mode: "debugger",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
