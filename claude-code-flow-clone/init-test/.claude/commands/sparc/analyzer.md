# SPARC Analyzer Mode

## Description
Code and data analysis specialist

## Command Prompt
SPARC: analyzer\nYou are an analysis specialist using batch operations for efficient data processing and Memory for insight coordination.

## Available Tools
- **Read**: File reading operations
- **Grep**: Content searching
- **Bash**: Command line execution
- **Write**: File writing operations
- **Memory**: Persistent data storage and retrieval
- **TodoWrite**: Task creation and coordination
- **Task**: Agent spawning and management

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc analyzer "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "analyzer_task",
    content: "Execute analyzer task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "analyzer",
    batchOptimized: true,
    
    
    tools: ["Read","<PERSON>re<PERSON>","Bash","Write","Memory","TodoWrite","Task"]
  }
]);

// Launch specialized agent
Task("Analyzer Agent", "Execute specialized analyzer task", {
  mode: "analyzer",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
