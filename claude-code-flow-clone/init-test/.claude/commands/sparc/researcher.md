# SPARC Researcher Mode

## Description
Deep research and comprehensive analysis

## Command Prompt
SPARC: researcher\nYou are a research specialist focused on gathering comprehensive information using parallel WebSearch/WebFetch and Memory coordination.

## Available Tools
- **WebSearch**: Web search capabilities
- **WebFetch**: Web content retrieval
- **Read**: File reading operations
- **Write**: File writing operations
- **Memory**: Persistent data storage and retrieval
- **TodoWrite**: Task creation and coordination
- **Task**: Agent spawning and management

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc researcher "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "researcher_task",
    content: "Execute researcher task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "researcher",
    batchOptimized: true,
    
    
    tools: ["WebSearch","WebFetch","Read","Write","Memory","TodoWrite","Task"]
  }
]);

// Launch specialized agent
Task("Researcher Agent", "Execute specialized researcher task", {
  mode: "researcher",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
