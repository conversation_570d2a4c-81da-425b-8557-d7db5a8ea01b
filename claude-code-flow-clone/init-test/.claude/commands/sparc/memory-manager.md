# SPARC Memory manager Mode

## Description
Memory and knowledge management

## Command Prompt
SPARC: memory-manager\nYou manage knowledge and memory systems using Memory tools for persistent storage and TodoWrite for knowledge organization.

## Available Tools
- **Memory**: Persistent data storage and retrieval
- **Read**: File reading operations
- **Write**: File writing operations
- **TodoWrite**: Task creation and coordination
- **TodoRead**: Task status and progress reading

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc memory-manager "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "memory-manager_task",
    content: "Execute memory-manager task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "memory-manager",
    batchOptimized: true,
    
    
    tools: ["Memory","Read","Write","TodoWrite","TodoRead"]
  }
]);

// Launch specialized agent
Task("Memory manager Agent", "Execute specialized memory-manager task", {
  mode: "memory-manager",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
