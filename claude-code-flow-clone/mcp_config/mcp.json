{"mcpServers": {"claude-flow": {"command": "npx", "args": ["-y", "claude-flow", "mcp", "serve"], "env": {"CLAUDE_FLOW_LOG_LEVEL": "info", "CLAUDE_FLOW_MCP_PORT": "3000"}, "capabilities": {"tools": true, "prompts": false, "resources": false}}, "claude-flow-advanced": {"command": "npx", "args": ["-y", "claude-flow", "mcp", "serve", "--config", "./claude-flow.config.json"], "env": {"CLAUDE_FLOW_LOG_LEVEL": "debug", "CLAUDE_FLOW_MCP_TRANSPORT": "stdio", "CLAUDE_FLOW_MEMORY_BACKEND": "sqlite", "CLAUDE_FLOW_TERMINAL_POOL_SIZE": "10"}, "capabilities": {"tools": true, "prompts": true, "resources": true}}, "claude-flow-minimal": {"command": "npx", "args": ["-y", "claude-flow@latest", "mcp", "serve", "--minimal"], "env": {"NODE_ENV": "production"}, "capabilities": {"tools": true}}}, "globalSettings": {"timeout": 30000, "retries": 3, "logDirectory": "./logs"}, "tools": {"agent": {"description": "Manage AI agents", "commands": ["spawn", "list", "terminate", "info"]}, "task": {"description": "Manage tasks and workflows", "commands": ["create", "list", "status", "cancel"]}, "memory": {"description": "Knowledge management", "commands": ["query", "store", "stats", "export"]}, "terminal": {"description": "Terminal session management", "commands": ["execute", "list", "create", "terminate"]}, "workflow": {"description": "Execute multi-step workflows", "commands": ["run", "status", "list"]}}, "examples": {"basic": {"description": "Basic MCP server setup", "command": "npx -y claude-flow mcp serve"}, "withConfig": {"description": "MCP server with custom configuration", "command": "npx -y claude-flow mcp serve --config ./my-config.json"}, "debugMode": {"description": "MCP server in debug mode", "command": "npx -y claude-flow mcp serve --log-level debug --verbose"}, "httpTransport": {"description": "MCP server with HTTP transport", "command": "npx -y claude-flow mcp serve --transport http --port 8080"}}}