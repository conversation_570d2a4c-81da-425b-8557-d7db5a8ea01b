# REST API

This REST API was created by the Claude Flow Swarm system.

## Swarm Details
- Swarm ID: swarm_aipkebfuq_nexjqmitd
- Generated: 2025-06-14T23:27:00.976Z

## Installation

```bash
npm install
```

## Usage

Start the server:
```bash
npm start
```

Development mode:
```bash
npm run dev
```

Run tests:
```bash
npm test
```

## API Endpoints

- `GET /health` - Health check
- `GET /api/v1/items` - Get all items
- `GET /api/v1/items/:id` - Get item by ID
- `POST /api/v1/items` - Create new item
- `PUT /api/v1/items/:id` - Update item
- `DELETE /api/v1/items/:id` - Delete item

---
Created by Claude Flow Swarm
