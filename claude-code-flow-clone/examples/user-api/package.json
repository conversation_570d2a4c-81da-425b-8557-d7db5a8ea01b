{"name": "rest-api", "version": "1.0.0", "description": "REST API created by Claude Flow Swarm", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["rest", "api", "swarm", "claude-flow"], "author": "Claude <PERSON> Swarm", "license": "MIT", "dependencies": {"express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "swarmMetadata": {"swarmId": "swarm_aipkebfuq_nexjqmitd", "created": "2025-06-14T23:27:00.974Z"}}