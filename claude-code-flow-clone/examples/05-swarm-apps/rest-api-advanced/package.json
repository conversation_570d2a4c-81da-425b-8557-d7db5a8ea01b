{"name": "rest-api-advanced", "version": "1.0.0", "description": "Advanced REST API with authentication, validation, logging, and best practices", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --coverage", "test:unit": "jest tests/unit --coverage", "test:integration": "jest tests/integration --runInBand", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "docs": "node src/utils/generateSwaggerDocs.js", "seed": "node src/seeders/index.js", "seed:products": "node src/seeders/products.seeder.js", "seed:orders": "node src/seeders/orders.seeder.js"}, "keywords": ["rest", "api", "express", "mongodb", "jwt", "authentication"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.6.3", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "joi": "^17.11.0", "winston": "^3.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^7.1.1", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "dotenv": "^16.3.1", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14", "hpp": "^0.2.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "mongodb-memory-server": "^9.0.1", "eslint": "^8.52.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jest": "^27.4.3", "@faker-js/faker": "^8.2.0"}, "engines": {"node": ">=16.0.0"}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "coveragePathIgnorePatterns": ["/node_modules/"], "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["./tests/setup.js"]}}