{"name": "swarm-app", "version": "1.0.0", "description": "Application created by <PERSON> S<PERSON>: build a real-time chat application with websockets and message history in ./examples/chat-app", "main": "app.js", "scripts": {"start": "node app.js"}, "keywords": ["swarm", "claude-flow"], "author": "Claude <PERSON> Swarm", "license": "MIT", "swarmMetadata": {"swarmId": "swarm_4vss8ckrh_ps9ptpmzk", "objective": "build a real-time chat application with websockets and message history in ./examples/chat-app", "created": "2025-06-14T23:28:44.922Z"}}