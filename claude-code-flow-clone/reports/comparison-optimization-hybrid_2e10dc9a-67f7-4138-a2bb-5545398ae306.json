{"id": "2e10dc9a-67f7-4138-a2bb-5545398ae306", "name": "comparison-optimization-hybrid", "description": "Performance comparison benchmark", "status": "completed", "config": {"name": "comparison-optimization-hybrid", "description": "Performance comparison benchmark", "strategy": "optimization", "mode": "hybrid", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 30, "max_retries": 2, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "2128f999-98f9-4824-bd09-8a83b6d27f17", "objective": "Optimize database query performance", "description": "Benchmark task: Optimize database query performance", "strategy": "optimization", "mode": "hybrid", "parameters": {}, "timeout": 30, "max_retries": 2, "priority": 1, "status": "pending", "created_at": "2025-06-14T20:02:36.410797", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "f14c0959-d822-4e16-829e-d432e315450d", "task_id": "2128f999-98f9-4824-bd09-8a83b6d27f17", "agent_id": "optimization-agent", "status": "success", "output": {"optimization_results": "Optimization completed for: Optimize database query performance", "performance_gain": "25% improvement", "optimizations_applied": ["caching", "indexing", "compression"]}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.180345, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 30.0, "memory_mb": 320, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T20:02:36.591257", "started_at": "2025-06-14T20:02:36.410809", "completed_at": "2025-06-14T20:02:36.591184", "duration": 0.180375}], "metrics": {"total_tasks": 1, "completed_tasks": 0, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.180345, "total_execution_time": 0.180345, "success_rate": 0.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T20:02:36.410775", "started_at": "2025-06-14T20:02:36.410779", "completed_at": "2025-06-14T20:02:36.591288", "duration": 0.180509, "error_log": [], "metadata": {}}