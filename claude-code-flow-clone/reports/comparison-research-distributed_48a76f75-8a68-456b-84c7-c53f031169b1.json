{"id": "48a76f75-8a68-456b-84c7-c53f031169b1", "name": "comparison-research-distributed", "description": "Performance comparison benchmark", "status": "completed", "config": {"name": "comparison-research-distributed", "description": "Performance comparison benchmark", "strategy": "research", "mode": "distributed", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 30, "max_retries": 2, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "dc0c1bd8-9527-4c98-bcaf-3fb41678f444", "objective": "Analyze market trends for AI startups", "description": "Benchmark task: Analyze market trends for AI startups", "strategy": "research", "mode": "distributed", "parameters": {}, "timeout": 30, "max_retries": 2, "priority": 1, "status": "pending", "created_at": "2025-06-14T20:02:31.068663", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "204d467d-d4a8-4425-a863-fd7d11a95a31", "task_id": "dc0c1bd8-9527-4c98-bcaf-3fb41678f444", "agent_id": "research-agent", "status": "success", "output": {"research_findings": "Research completed for: Analyze market trends for AI startups", "sources": ["academic papers", "documentation", "best practices"], "methodology": "comprehensive analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.10024, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 15.0, "memory_mb": 128, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T20:02:31.169014", "started_at": "2025-06-14T20:02:31.068703", "completed_at": "2025-06-14T20:02:31.168978", "duration": 0.100275}], "metrics": {"total_tasks": 1, "completed_tasks": 0, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.10024, "total_execution_time": 0.10024, "success_rate": 0.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T20:02:31.068690", "started_at": "2025-06-14T20:02:31.068695", "completed_at": "2025-06-14T20:02:31.169040", "duration": 0.100345, "error_log": [], "metadata": {}}