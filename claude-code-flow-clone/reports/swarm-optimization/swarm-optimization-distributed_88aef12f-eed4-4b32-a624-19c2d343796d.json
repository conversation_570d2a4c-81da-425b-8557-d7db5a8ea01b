{"id": "88aef12f-eed4-4b32-a624-19c2d343796d", "name": "swarm-optimization-distributed", "description": "Benchmark: Analyze swarm performance bottlenecks and optimization opportunities", "status": "completed", "config": {"name": "swarm-optimization-distributed", "description": "Benchmark: Analyze swarm performance bottlenecks and optimization opportunities", "strategy": "optimization", "mode": "distributed", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "../reports/swarm-optimization", "verbose": false}, "tasks": [{"id": "729c82fa-6572-4b74-98fb-09e995369771", "objective": "Analyze swarm performance bottlenecks and optimization opportunities", "description": "Benchmark task: Analyze swarm performance bottlenecks and optimization opportunities", "strategy": "optimization", "mode": "distributed", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T18:38:24.283434", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "c701beda-77ce-45b2-920b-0f4cc7a8524e", "task_id": "729c82fa-6572-4b74-98fb-09e995369771", "agent_id": "optimization-agent", "status": "success", "output": {"optimization_results": "Optimization completed for: Analyze swarm performance bottlenecks and optimization opportunities", "performance_gain": "25% improvement", "optimizations_applied": ["caching", "indexing", "compression"]}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.180329, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 30.0, "memory_mb": 320, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T18:38:24.463869", "started_at": "2025-06-14T18:38:24.283478", "completed_at": "2025-06-14T18:38:24.463832", "duration": 0.180354}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.180329, "total_execution_time": 0.180329, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T18:38:24.283462", "started_at": "2025-06-14T18:38:24.283468", "completed_at": "2025-06-14T18:38:24.463893", "duration": 0.180425, "error_log": [], "metadata": {}}