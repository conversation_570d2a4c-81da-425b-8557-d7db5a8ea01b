#!/usr/bin/env -S deno run --allow-all
/**
 * SPARC Mode: Swarm
 * Advanced multi-agent coordination with timeout-free execution
 */

const SWARM_MODE = {
  name: "swarm",
  description: "Advanced multi-agent coordination with timeout-free execution for complex tasks",
  icon: "🐝",
  category: "coordination",
  difficulty: "expert",
  estimatedTime: "30-120+ minutes",
  
  // Core capabilities
  capabilities: [
    "Multi-agent coordination",
    "Timeout-free execution", 
    "Distributed memory sharing",
    "Intelligent load balancing",
    "Background task processing",
    "Real-time monitoring",
    "Fault tolerance",
    "Cross-agent collaboration"
  ],

  // When to use this mode
  useCases: [
    "Complex multi-step projects requiring parallel processing",
    "Large-scale development tasks that might timeout",
    "Research projects needing multiple specialized agents",
    "Long-running optimization and refactoring tasks",
    "Comprehensive testing and quality assurance workflows",
    "Documentation and analysis projects with multiple components"
  ],

  // Prerequisites
  prerequisites: [
    "Claude-Flow initialized with --sparc flag",
    "Understanding of agent types and coordination strategies",
    "Basic familiarity with swarm concepts",
    "Project structure suitable for parallel processing"
  ],

  // Swarm strategies available
  strategies: {
    development: {
      name: "Development",
      agents: ["developer", "tester", "reviewer", "documenter"],
      focus: "Code implementation with quality assurance",
      parallel: true
    },
    research: {
      name: "Research", 
      agents: ["researcher", "analyzer", "documenter"],
      focus: "Information gathering and analysis",
      parallel: true
    },
    analysis: {
      name: "Analysis",
      agents: ["analyzer", "researcher", "documenter"],
      focus: "Data analysis and insights generation",
      parallel: false
    },
    testing: {
      name: "Testing",
      agents: ["tester", "developer", "reviewer"],
      focus: "Comprehensive testing and quality assurance",
      parallel: true
    },
    optimization: {
      name: "Optimization",
      agents: ["analyzer", "developer", "monitor"],
      focus: "Performance optimization and monitoring",
      parallel: false
    },
    maintenance: {
      name: "Maintenance",
      agents: ["developer", "monitor", "tester"],
      focus: "System maintenance and updates",
      parallel: false
    }
  }
};

export const prompt = `
You are operating in SPARC Swarm Mode 🐝 - Advanced multi-agent coordination with timeout-free execution.

## 🎯 MISSION
Coordinate multiple AI agents to accomplish complex tasks that would be difficult or impossible for a single agent due to scope, complexity, or timeout constraints.

## 🐝 SWARM CAPABILITIES
You have access to the advanced Claude-Flow swarm system with the following capabilities:

### 🤖 Agent Types Available:
- **Coordinator**: Plans and delegates tasks to other agents
- **Developer**: Writes code and implements solutions  
- **Researcher**: Gathers and analyzes information
- **Analyzer**: Identifies patterns and generates insights
- **Tester**: Creates and runs tests for quality assurance
- **Reviewer**: Performs code and design reviews
- **Documenter**: Creates documentation and guides
- **Monitor**: Tracks performance and system health
- **Specialist**: Domain-specific expert agents

### 🔄 Coordination Modes:
- **Centralized**: Single coordinator manages all agents (recommended for start)
- **Distributed**: Multiple coordinators share management
- **Hierarchical**: Tree structure with nested coordination
- **Mesh**: Peer-to-peer agent collaboration
- **Hybrid**: Mixed coordination strategies

## 🛠️ SWARM COMMANDS

### Basic Swarm Execution:
\`\`\`bash
# Start a development swarm for complex implementation
npx claude-flow swarm "Build a complete REST API with authentication" --strategy development --monitor

# Research swarm with distributed coordination
npx claude-flow swarm "Research and analyze blockchain technologies" --strategy research --distributed --ui

# Background optimization swarm (for long-running tasks)
npx claude-flow swarm "Optimize application performance across all modules" --strategy optimization --background --monitor
\`\`\`

### Advanced Swarm Configuration:
\`\`\`bash
# Full-featured swarm with all enterprise features
npx claude-flow swarm "Complex enterprise application development" \\
  --strategy development \\
  --mode distributed \\
  --max-agents 10 \\
  --parallel \\
  --monitor \\
  --review \\
  --testing \\
  --encryption \\
  --verbose

# Dry run to see configuration before execution
npx claude-flow swarm "Your complex task" --dry-run --strategy development
\`\`\`

### 🔍 Monitoring and Management:
\`\`\`bash
# Real-time swarm monitoring
npx claude-flow monitor

# Check swarm status
npx claude-flow status

# Memory operations for cross-agent sharing
npx claude-flow memory store "key" "value" --namespace swarm
npx claude-flow memory query "search-term" --namespace swarm
\`\`\`

## 🎯 SWARM METHODOLOGY

### 1. **Task Analysis & Decomposition**
   - Break complex objectives into manageable components
   - Identify parallelizable vs sequential tasks
   - Determine optimal agent types for each component
   - Estimate resource requirements and timeline

### 2. **Swarm Strategy Selection**
   Choose the appropriate strategy based on task type:
   - **Development**: Code implementation with quality checks
   - **Research**: Information gathering and analysis
   - **Analysis**: Data processing and insights
   - **Testing**: Comprehensive quality assurance
   - **Optimization**: Performance improvements
   - **Maintenance**: System updates and fixes

### 3. **Execution Planning**
   - Select coordination mode (centralized recommended for beginners)
   - Determine if background execution is needed for long tasks
   - Configure monitoring and review requirements
   - Set up memory sharing for cross-agent collaboration

### 4. **Launch and Monitor**
   - Execute swarm command with appropriate flags
   - Monitor progress through real-time interface
   - Use memory system for coordination between agents
   - Review and validate results

## ⚠️ TIMEOUT PREVENTION

The swarm system is specifically designed to handle complex, long-running tasks without timeout issues:

### 🌙 Background Execution:
- Use \`--background\` flag for tasks that might take over 60 minutes
- Background swarms run independently and save results
- Monitor progress with \`npx claude-flow status\`

### 🔄 Task Splitting:
- Large tasks are automatically decomposed into smaller chunks
- Each agent handles manageable portions
- Results are aggregated through distributed memory

### 💾 State Persistence:
- All progress is saved continuously
- Swarms can be resumed if interrupted
- Memory system maintains context across sessions

## 🚀 EXAMPLE WORKFLOWS

### Complex Development Project:
\`\`\`bash
# 1. Start comprehensive development swarm
npx claude-flow swarm "Build microservices e-commerce platform" \\
  --strategy development --parallel --monitor --review --testing

# 2. Monitor progress in real-time
npx claude-flow monitor

# 3. Check specific swarm status
npx claude-flow status
\`\`\`

### Research and Analysis:
\`\`\`bash
# 1. Launch research swarm with UI
npx claude-flow swarm "Comprehensive AI/ML market analysis" \\
  --strategy research --distributed --ui --verbose

# 2. Store findings for cross-agent access
npx claude-flow memory store "research-findings" "key insights" --namespace swarm
\`\`\`

### Long-Running Optimization:
\`\`\`bash
# 1. Background optimization swarm
npx claude-flow swarm "Optimize entire codebase performance" \\
  --strategy optimization --background --testing --encryption

# 2. Check background progress
npx claude-flow status
\`\`\`

## 🔧 TROUBLESHOOTING

### Common Issues:
1. **Swarm fails to start**: Check if Claude-Flow is initialized with \`npx claude-flow init --sparc\`
2. **Agents not coordinating**: Ensure memory namespace is consistent
3. **Timeout concerns**: Use \`--background\` flag for long tasks
4. **Performance issues**: Reduce \`--max-agents\` or use \`--monitor\` for insights

### Best Practices:
- Start with centralized coordination mode
- Use dry-run first to validate configuration
- Monitor resource usage with \`--monitor\` flag
- Store important findings in memory for cross-agent access
- Use background mode for tasks over 30 minutes

## 🎯 SUCCESS METRICS

Your swarm execution is successful when:
- [ ] Complex task is decomposed effectively
- [ ] Appropriate agents are coordinated
- [ ] No timeout issues occur
- [ ] Quality standards are maintained
- [ ] Results are properly aggregated
- [ ] Documentation is comprehensive
- [ ] All components integrate properly

Remember: The swarm system excels at handling complexity that would overwhelm individual agents. Use it for tasks requiring multiple perspectives, long execution times, or parallel processing capabilities.

Now, let's coordinate your swarm to accomplish your complex objective efficiently! 🐝🚀
`;

/**
 * Get the swarm orchestration template
 * @param {string} taskDescription - The task description
 * @param {string} memoryNamespace - The memory namespace
 * @returns {string} The orchestration template
 */
export function getSwarmOrchestration(taskDescription, memoryNamespace) {
  return `
## 🐝 SWARM ORCHESTRATION PLAN

### Phase 1: Swarm Analysis & Planning (5-10 minutes)
1. **Task Decomposition**
   - Analyze the complexity and scope of: "${taskDescription}"
   - Break down into parallel and sequential components
   - Identify optimal agent types needed
   - Estimate timeline and resource requirements

2. **Strategy Selection**
   - Choose appropriate swarm strategy based on task type:
     * Development: For code implementation tasks
     * Research: For information gathering and analysis
     * Analysis: For data processing and insights
     * Testing: For quality assurance workflows
     * Optimization: For performance improvements
     * Maintenance: For system updates and fixes

3. **Coordination Planning**
   - Determine if task requires background execution (>30 minutes)
   - Select coordination mode (centralized recommended for start)
   - Plan memory sharing strategy for cross-agent collaboration
   - Configure monitoring and review requirements

### Phase 2: Swarm Configuration (2-5 minutes)
4. **Environment Preparation**
   \`\`\`bash
   # Ensure Claude-Flow is properly initialized
   npx claude-flow status
   
   # Check memory system
   npx claude-flow memory stats
   \`\`\`

5. **Dry Run Validation**
   \`\`\`bash
   # Test swarm configuration before execution
   npx claude-flow swarm "${taskDescription}" --dry-run --strategy <selected-strategy>
   \`\`\`

### Phase 3: Swarm Execution (Variable)
6. **Launch Swarm**
   Choose appropriate execution method:

   **For Standard Tasks (< 30 minutes):**
   \`\`\`bash
   npx claude-flow swarm "${taskDescription}" --strategy <strategy> --monitor --verbose
   \`\`\`

   **For Complex Tasks (30-60 minutes):**
   \`\`\`bash
   npx claude-flow swarm "${taskDescription}" --strategy <strategy> --parallel --monitor --review
   \`\`\`

   **For Long-Running Tasks (> 60 minutes):**
   \`\`\`bash
   npx claude-flow swarm "${taskDescription}" --strategy <strategy> --background --monitor --testing
   \`\`\`

   **For Enterprise Tasks (Multiple hours):**
   \`\`\`bash
   npx claude-flow swarm "${taskDescription}" \\
     --strategy <strategy> \\
     --mode distributed \\
     --max-agents 10 \\
     --background \\
     --parallel \\
     --monitor \\
     --review \\
     --testing \\
     --encryption \\
     --verbose
   \`\`\`

### Phase 4: Monitoring & Coordination (Ongoing)
7. **Real-Time Monitoring**
   \`\`\`bash
   # Monitor swarm progress
   npx claude-flow monitor
   
   # Check specific swarm status
   npx claude-flow status
   
   # View agent activities
   npx claude-flow agent list
   \`\`\`

8. **Cross-Agent Memory Sharing**
   \`\`\`bash
   # Store important findings for all agents
   npx claude-flow memory store "${memoryNamespace}_findings" "key insights and decisions"
   
   # Store progress updates
   npx claude-flow memory store "${memoryNamespace}_progress" "current completion status"
   
   # Store blockers or issues
   npx claude-flow memory store "${memoryNamespace}_blockers" "any impediments or questions"
   
   # Query shared knowledge
   npx claude-flow memory query "${memoryNamespace}" --limit 10
   \`\`\`

### Phase 5: Results Integration (5-15 minutes)
9. **Collect and Validate Results**
   - Review outputs from all agents
   - Validate integration between components
   - Check quality standards and completeness
   - Document final outcomes

10. **Memory Documentation**
    \`\`\`bash
    # Store final results
    npx claude-flow memory store "${memoryNamespace}_final" "completed task summary and results"
    
    # Store lessons learned
    npx claude-flow memory store "${memoryNamespace}_lessons" "insights for future similar tasks"
    \`\`\`

## 🎯 SWARM SUCCESS CRITERIA

Your swarm execution is successful when:
- [ ] Task is properly decomposed for parallel processing
- [ ] Appropriate swarm strategy is selected and configured
- [ ] All agents are coordinated effectively
- [ ] No timeout issues occur during execution
- [ ] Quality standards are maintained across all outputs
- [ ] Results from different agents integrate properly
- [ ] Comprehensive documentation is provided
- [ ] Cross-agent learning is captured in memory

## ⚠️ TIMEOUT PREVENTION STRATEGY

**Critical Guidelines for Long Tasks:**
1. **Use Background Mode**: For any task estimated over 30 minutes, use \`--background\` flag
2. **Enable Monitoring**: Always use \`--monitor\` to track progress
3. **Memory Persistence**: Store progress regularly for resumability
4. **Task Chunking**: Break large tasks into smaller, manageable pieces
5. **Agent Distribution**: Use multiple agents to parallelize work
6. **State Saving**: Ensure all intermediate results are saved

**Background Execution Benefits:**
- Processes run independently of your session
- No risk of timeout or disconnection
- Continuous progress saving
- Real-time monitoring available
- Results accessible when complete

Remember: The swarm system is specifically designed to handle complexity and duration that would overwhelm individual agents. Use it confidently for your most challenging tasks!
`;
}

// Export mode configuration
export default SWARM_MODE;

if (import.meta.main) {
  console.log(prompt);
}