{"name": "@claude-flow/migration", "version": "1.0.0", "description": "Migration system for claude-flow projects", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"claude-flow-migrate": "dist/index.js"}, "scripts": {"build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["claude-flow", "migration", "prompts", "optimization", "automation"], "author": "Claude Flow Team", "license": "MIT", "dependencies": {"commander": "^11.0.0", "fs-extra": "^11.0.0", "glob": "^10.0.0", "inquirer": "^9.0.0", "chalk": "^5.0.0"}, "devDependencies": {"@types/fs-extra": "^11.0.0", "@types/inquirer": "^9.0.0", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "rimraf": "^5.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.test.ts", "!src/**/*.d.ts"], "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"]}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/ruvnet/claude-code-flow", "directory": "src/migration"}, "bugs": {"url": "https://github.com/ruvnet/claude-code-flow/issues"}, "homepage": "https://github.com/ruvnet/claude-code-flow#readme"}